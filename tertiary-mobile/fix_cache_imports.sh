#!/bin/bash

# Script to remove cache-related imports and simplify Drift local sources

echo "Fixing cache imports in local sources..."

# Files to fix
files=(
    "lib/features/courses/data/sources/local_courses_source.dart"
    "lib/features/courses/data/sources/local_course_specifications_source.dart"
)

for file in "${files[@]}"; do
    if [ -f "$file" ]; then
        echo "Processing $file..."
        
        # Remove cache imports
        sed -i '' '/cache_manager\.dart/d' "$file"
        sed -i '' '/cache_policy\.dart/d' "$file"
        
        # Remove CacheManager from constructor and provider
        sed -i '' 's/, cacheManager//g' "$file"
        sed -i '' 's/cacheManager, //g' "$file"
        sed -i '' 's/final cacheManager = ref\.read(cacheManagerProvider);//g' "$file"
        sed -i '' 's/final CacheManager _cacheManager;//g' "$file"
        sed -i '' 's/, this\._cacheManager//g' "$file"
        sed -i '' 's/this\._cacheManager, //g' "$file"
        
        echo "Fixed $file"
    else
        echo "File $file not found"
    fi
done

echo "Done!"
