#!/bin/bash

# Set error handling
set -e
set -u
set -o pipefail

# Check build type argument
if [[ $# -ne 1 ]]; then
    echo "Usage: $0 <build_type>"
    echo "Build types: apk, appbundle, ipa"
    exit 1
fi

BUILD_TYPE="$1"
CONFIG_FILE="university_config.json"

# Validate build type
valid_build_types=("apk" "appbundle" "ipa")
if [[ ! " ${valid_build_types[@]} " =~ " ${BUILD_TYPE} " ]]; then
    echo "Invalid build type. Choose from: ${valid_build_types[*]}"
    exit 1
fi

# Verify config file exists
if [[ ! -f "$CONFIG_FILE" ]]; then
    echo "Error: Configuration file $CONFIG_FILE not found!"
    exit 1
fi

# Validate JSON
if ! jq empty "$CONFIG_FILE" >/dev/null 2>&1; then
    echo "Error: Invalid JSON in $CONFIG_FILE"
    exit 1
fi

# Compact and escape the JSON
UNIVERSITY_CONFIG=$(jq -c . "$CONFIG_FILE")

# Extract university name for display
UNIVERSITY_NAME=$(jq -r '.name // .universityName // "Unknown University"' "$CONFIG_FILE")

# Check if FVM is installed
if command -v fvm &> /dev/null; then
    FLUTTER_CMD="fvm flutter"
else
    echo "FVM not found. Using direct flutter command."
    FLUTTER_CMD="flutter"
fi

# Validate Flutter installation
if ! command -v $FLUTTER_CMD &> /dev/null; then
    echo "Error: Flutter is not installed!"
    exit 1
fi

# Build command
echo "Building $BUILD_TYPE for $UNIVERSITY_NAME"
$FLUTTER_CMD build "$BUILD_TYPE" \
    --dart-define=UNIVERSITY_CONFIG="$UNIVERSITY_CONFIG"

echo "Build completed successfully!"