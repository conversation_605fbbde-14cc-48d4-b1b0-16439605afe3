import 'package:drift/drift.dart';
import 'package:drift_flutter/drift_flutter.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:tertiary_mobile/core/utils/logger.dart';
import 'package:tertiary_mobile/features/announcement/data/models/announcements_table.dart';
import 'package:tertiary_mobile/shared/data/models/sessions_table.dart';
import 'package:tertiary_mobile/shared/data/models/semesters_table.dart';
import 'package:tertiary_mobile/features/courses/data/tables/courses_table.dart';
import 'package:tertiary_mobile/features/courses/data/tables/course_registrations_table.dart';
import 'package:tertiary_mobile/features/courses/data/tables/course_specifications_table.dart';
import 'package:tertiary_mobile/features/courses/data/tables/credit_load_limits_table.dart';

part 'local_database.g.dart';

@DriftDatabase(
  tables: [
    AnnouncementsTable,
    SessionsTable,
    SemestersTable,
    CoursesTable,
    CourseRegistrationsTable,
    CourseSpecificationsTable,
    CreditLoadLimitsTable,
  ],
)
class LocalDatabase extends _$LocalDatabase {
  LocalDatabase([QueryExecutor? executor])
      : super(executor ?? _openConnection());

  @override
  int get schemaVersion => 1;

  /// Clear all data from the database (used on logout)
  Future<void> clearAllData() async {
    await transaction(() async {
      // Clear all tables in reverse dependency order
      await delete(creditLoadLimitsTable).go();
      await delete(courseSpecificationsTable).go();
      await delete(courseRegistrationsTable).go();
      await delete(coursesTable).go();
      await delete(semestersTable).go();
      await delete(sessionsTable).go();
      await delete(announcementsTable).go();

      logger.i('All database data cleared');
    });
  }

  @override
  MigrationStrategy get migration => MigrationStrategy(
        onCreate: (m) async {
          await m.createAll();
          logger.i('Database created with schema version 1');
        },
        beforeOpen: (details) async {
          if (details.wasCreated) {
            logger.i('Database created with version ${details.versionNow}');
          }
          // Enable foreign keys
          await customStatement('PRAGMA foreign_keys = ON');
        },
      );
}

QueryExecutor _openConnection() {
  return driftDatabase(name: 'tertiary_mobile_db');
}

@Riverpod(keepAlive: true)
LocalDatabase localDatabase(Ref ref) {
  return LocalDatabase();
}
