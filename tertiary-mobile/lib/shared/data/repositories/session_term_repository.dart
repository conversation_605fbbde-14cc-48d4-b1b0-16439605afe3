import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:tertiary_mobile/core/network/providers/internet_checker.dart';
import '../models/session_semester_model.dart';
import '../sources/remote_session_term_source.dart';
import '../sources/local_session_term_source.dart';

part 'session_term_repository.g.dart';

@riverpod
SessionTermRepository sessionTermRepository(Ref ref) {
  final remote = ref.read(remoteSessionTermSourceProvider);
  final local = ref.read(localSessionTermSourceProvider);
  final internetChecker = ref.read(internetCheckerProvider);
  return SessionTermRepository(remote, local, internetChecker);
}

class SessionTermRepository {
  final RemoteSessionTermSource remote;
  final LocalSessionTermSource local;
  final InternetConnectionChecker internetChecker;

  SessionTermRepository(
    this.remote,
    this.local,
    this.internetChecker,
  );

  Future<List<Session>> fetchSessions() async {
    final hasConnection = await internetChecker.hasConnection;

    try {
      if (hasConnection) {
        final sessions = await remote.fetchSessions();
        await local.saveSessions(sessions);
        return sessions;
      } else {
        final cached = await local.getSessions();
        return cached;
      }
    } catch (_) {
      try {
        final cached = await local.getSessions();
        return cached;
      } catch (e2) {
        rethrow;
      }
    }
  }

  Future<List<Session>> getCachedSessions() async {
    return local.getSessions();
  }

  /// Implements stale-while-revalidate pattern:
  /// 1. Returns cached data immediately if available
  /// 2. Triggers background refresh if online
  /// 3. Returns fresh data when background refresh completes
  Stream<List<Session>> fetchSessionsStaleWhileRevalidate() async* {
    // First, yield cached data immediately if available
    final cachedSessions = await local.getSessions();
    if (cachedSessions.isNotEmpty) {
      yield cachedSessions;
    }

    // Then attempt to fetch fresh data in the background
    final hasConnection = await internetChecker.hasConnection;
    if (hasConnection) {
      try {
        final freshSessions = await remote.fetchSessions();
        await local.saveSessions(freshSessions);
        yield freshSessions;
      } catch (_) {
        // If fresh fetch fails and we haven't yielded cached data yet, yield it now
        if (cachedSessions.isEmpty) {
          final fallbackSessions = await local.getSessions();
          yield fallbackSessions;
        }
        // If we already yielded cached data, we don't need to do anything
        // The UI will continue showing the cached data
      }
    } else if (cachedSessions.isEmpty) {
      // No connection and no cached data - yield empty list
      yield <Session>[];
    }
  }
}
