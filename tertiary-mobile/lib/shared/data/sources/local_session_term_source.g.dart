// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'local_session_term_source.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$localSessionTermSourceHash() =>
    r'fb0bbc06386b79ec0749259b4a107571597b928c';

/// See also [localSessionTermSource].
@ProviderFor(localSessionTermSource)
final localSessionTermSourceProvider =
    AutoDisposeProvider<LocalSessionTermSource>.internal(
  localSessionTermSource,
  name: r'localSessionTermSourceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$localSessionTermSourceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef LocalSessionTermSourceRef
    = AutoDisposeProviderRef<LocalSessionTermSource>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
