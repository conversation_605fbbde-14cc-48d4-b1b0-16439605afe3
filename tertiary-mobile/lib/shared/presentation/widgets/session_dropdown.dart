import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../../providers/session_semester_provider.dart';
import '../../../features/authentication/providers/auth_provider.dart';
import 'dropdown.dart';

class SessionDropdown extends ConsumerWidget {
  final String? label;
  final int? selectedSessionId;
  final void Function(int?) onChanged;
  final void Function(int?)? onDataAvailable;

  const SessionDropdown({
    super.key,
    this.label,
    required this.selectedSessionId,
    required this.onChanged,
    this.onDataAvailable,
  });

  static int? getLastSessionId(List<dynamic> sessions) {
    if (sessions.isEmpty) return null;
    return sessions.last.id;
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final sessionsAsync = ref.watch(sessionSemesterNotifierProvider);
    final userAsync = ref.watch(authProvider);

    return sessionsAsync.when(
      loading: () => StryDropdown(
        label: label ?? 'Select Session',
        items: const ['Loading...'],
        selectedValue: 'Loading...',
        onChanged: (_) {},
        enabled: false,
      ),
      error: (e, _) => StryDropdown(
        label: label ?? 'Select Session',
        items: const ['Failed to load'],
        selectedValue: 'Failed to load',
        onChanged: (_) {},
        enabled: false,
      ),
      data: (sessions) {
        if (sessions.isEmpty) {
          return StryDropdown(
            label: label ?? 'Select Session',
            items: const ['No sessions available'],
            selectedValue: 'No sessions available',
            onChanged: (_) {},
            enabled: false,
          );
        }

        // Remove duplicates and create unique items
        final uniqueSessions = <String, dynamic>{};
        for (final session in sessions) {
          // Use session name as key to remove duplicates
          // Keep the most recent session if there are duplicates
          uniqueSessions[session.sessionName] = session;
        }

        final uniqueSessionsList = uniqueSessions.values.toList();

        // Sort sessions chronologically (newest first)
        uniqueSessionsList.sort((a, b) {
          // Extract years from session names like "2024/2025", "2023/2024"
          final aYears = _extractYearsFromSessionName(a.sessionName);
          final bYears = _extractYearsFromSessionName(b.sessionName);

          // Compare by the first year (start year) in descending order
          return bYears.first.compareTo(aYears.first);
        });

        final items =
            uniqueSessionsList.map((s) => s.sessionName as String).toList();

        // Debug logging
        if (kDebugMode) {
          print('SessionDropdown Debug:');
          print('  Total sessions: ${sessions.length}');
          print('  Unique sessions: ${uniqueSessionsList.length}');
          print('  Selected session ID: $selectedSessionId');
          print('  Items: $items');
          if (sessions.length != uniqueSessionsList.length) {
            print('  WARNING: Duplicate session names detected!');
            final duplicates = <String>[];
            final seen = <String>{};
            for (final session in sessions) {
              if (seen.contains(session.sessionName)) {
                duplicates.add(session.sessionName);
              }
              seen.add(session.sessionName);
            }
            print('  Duplicate names: $duplicates');
          }
        }

        String? selectedName;
        bool shouldAutoSelect = false;

        if (selectedSessionId != null) {
          final found =
              uniqueSessionsList.where((s) => s.id == selectedSessionId);
          if (found.isNotEmpty) {
            selectedName = found.first.sessionName;
          } else {
            // Selected session not found in current data, reset to first available
            selectedName = uniqueSessionsList.first.sessionName;
            shouldAutoSelect = true;
          }
        } else {
          // No session selected, auto-select first available
          selectedName = uniqueSessionsList.first.sessionName;
          shouldAutoSelect = true;
        }

        // Ensure selectedName is in the items list
        if (selectedName != null && !items.contains(selectedName)) {
          selectedName = items.isNotEmpty ? items.first : null;
          shouldAutoSelect = true;
        }

        // Perform auto-selection if needed
        if (shouldAutoSelect && uniqueSessionsList.isNotEmpty) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            // Small delay to ensure proper state coordination
            Future.delayed(const Duration(milliseconds: 50), () {
              // Get user's current session if available
              final user = userAsync.valueOrNull;
              dynamic defaultSession;

              if (user?.currentAcademicSessionWithSemesters != null) {
                // Try to find the user's current session in the list
                final currentSessionId =
                    user!.currentAcademicSessionWithSemesters!.id;
                final currentSessionMatch =
                    uniqueSessionsList.where((s) => s.id == currentSessionId);

                if (currentSessionMatch.isNotEmpty) {
                  // Use user's current session as default
                  defaultSession = currentSessionMatch.first;
                } else {
                  // Fallback to first session if current session not found
                  defaultSession = uniqueSessionsList.first;
                }
              } else {
                // No user current session, default to first session (most recent)
                defaultSession = uniqueSessionsList.first;
              }

              onChanged(defaultSession.id);
              if (onDataAvailable != null) {
                onDataAvailable!(defaultSession.id);
              }
            });
          });
        }

        return StryDropdown(
          label: label ?? 'Select Session',
          items: items,
          selectedValue: selectedName,
          onChanged: (val) {
            final selected = uniqueSessionsList.firstWhere(
              (s) => s.sessionName == val,
              orElse: () => uniqueSessionsList.first,
            );
            onChanged(selected.id);
          },
        );
      },
    );
  }

  /// Extract years from session name like "2024/2025" -> [2024, 2025]
  List<int> _extractYearsFromSessionName(String sessionName) {
    final regex = RegExp(r'(\d{4})');
    final matches = regex.allMatches(sessionName);
    final years = matches.map((match) => int.parse(match.group(1)!)).toList();

    // If we can't parse years, return current year to avoid errors
    if (years.isEmpty) {
      final currentYear = DateTime.now().year;
      return [currentYear, currentYear + 1];
    }

    // Ensure we have at least 2 years, add next year if only one found
    if (years.length == 1) {
      years.add(years.first + 1);
    }

    return years;
  }
}
