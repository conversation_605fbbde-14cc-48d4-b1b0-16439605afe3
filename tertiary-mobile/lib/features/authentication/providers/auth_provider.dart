import 'dart:convert';

import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:tertiary_mobile/core/utils/local_database.dart';
import 'package:tertiary_mobile/core/utils/secure_storage.dart';
import 'package:tertiary_mobile/core/utils/shared_preferences.dart';
import 'package:tertiary_mobile/features/authentication/data/models/user.dart';
import 'package:tertiary_mobile/shared/data/models/session_semester_model.dart';

part 'auth_provider.g.dart';

@Riverpod(keepAlive: true)
class Auth extends _$Auth {
  late final SecureStorage _storage;

  @override
  FutureOr<User?> build() async {
    _storage = ref.read(secureStorageProvider);
    return _initialize();
  }

  Future<User?> _initialize() async {
    try {
      final token = await _storage.getToken();
      final userJson = await _storage.getUser();

      if (token != null && userJson != null) {
        return User.fromJson(jsonDecode(userJson));
      } else {
        return null;
      }
    } catch (e) {
      rethrow;
    }
  }

  Future<void> login(String token, User user, Session session) async {
    state = const AsyncValue.loading();
    try {
      // Update user with current session/semester before saving
      final updatedUser = user.copyWith(
        currentAcademicSessionWithSemesters: session,
      );
      await _storage.saveToken(token);
      await _storage.saveUser(jsonEncode(updatedUser.toJson()));
      state = AsyncValue.data(updatedUser);
    } catch (e) {
      rethrow;
    }
  }

  Future<void> logout() async {
    state = const AsyncValue.loading();
    await _storage.clearAll();
    await SharedPreferencesService.clear();
    await _clearAllDatabaseData();
    state = const AsyncValue.data(null);
  }

  Future<void> _clearAllDatabaseData() async {
    final db = ref.read(localDatabaseProvider);

    await db.transaction(() async {
      // Delete all data from all tables
      await db.delete(db.announcementsTable).go();
      await db.delete(db.sessionsTable).go();
      await db.delete(db.semestersTable).go();
      await db.delete(db.coursesTable).go();
      await db.delete(db.courseRegistrationsTable).go();
      await db.delete(db.courseSpecificationsTable).go();
    });
  }
}
