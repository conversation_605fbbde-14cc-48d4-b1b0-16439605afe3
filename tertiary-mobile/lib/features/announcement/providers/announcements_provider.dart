import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../data/models/announcement_model.dart';
import '../data/repositories/announcements_repository.dart';

part 'announcements_provider.g.dart';

@riverpod
class Announcements extends _$Announcements {
  @override
  Future<List<Announcement>> build() async {
    final repository = ref.read(announcementsRepositoryProvider);
    return await repository.fetchAnnouncements();
  }

  Future<void> refresh(String sessionId) async {
    // For manual refresh, show loading state and fetch fresh data
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      final repository = ref.read(announcementsRepositoryProvider);
      return await repository.fetchAnnouncements();
    });
  }
}
