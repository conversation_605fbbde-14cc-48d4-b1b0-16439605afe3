import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:tertiary_mobile/core/network/providers/internet_checker.dart';
import '../models/announcement_model.dart';
import '../sources/remote_announcements_source.dart';
import '../sources/local_announcements_source.dart';

part 'announcements_repository.g.dart';

@riverpod
AnnouncementsRepository announcementsRepository(Ref ref) {
  final remote = ref.read(remoteAnnouncementsSourceProvider);
  final local = ref.read(localAnnouncementsSourceProvider);
  final internetChecker = ref.read(internetCheckerProvider);
  return AnnouncementsRepository(remote, local, internetChecker);
}

class AnnouncementsRepository {
  final RemoteAnnouncementsSource remote;
  final LocalAnnouncementsSource local;
  final InternetConnectionChecker internetChecker;

  AnnouncementsRepository(
    this.remote,
    this.local,
    this.internetChecker,
  );

  Future<List<Announcement>> fetchAnnouncements() async {
    final hasConnection = await internetChecker.hasConnection;

    if (hasConnection) {
      try {
        final announcements = await remote.fetchAnnouncements();
        await local.saveAnnouncements(announcements);
        return announcements;
      } catch (_) {
        return await local.getAnnouncements();
      }
    } else {
      return await local.getAnnouncements();
    }
  }
}
