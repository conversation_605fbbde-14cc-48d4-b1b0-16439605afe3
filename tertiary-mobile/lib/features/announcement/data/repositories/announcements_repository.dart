import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:tertiary_mobile/core/network/providers/internet_checker.dart';
import '../models/announcement_model.dart';
import '../sources/remote_announcements_source.dart';
import '../sources/local_announcements_source.dart';

part 'announcements_repository.g.dart';

@riverpod
AnnouncementsRepository announcementsRepository(Ref ref) {
  final remote = ref.read(remoteAnnouncementsSourceProvider);
  final local = ref.read(localAnnouncementsSourceProvider);
  final internetChecker = ref.read(internetCheckerProvider);
  return AnnouncementsRepository(remote, local, internetChecker);
}

class AnnouncementsRepository {
  final RemoteAnnouncementsSource remote;
  final LocalAnnouncementsSource local;
  final InternetConnectionChecker internetChecker;

  AnnouncementsRepository(
    this.remote,
    this.local,
    this.internetChecker,
  );

  Future<List<Announcement>> fetchAnnouncements() async {
    final hasConnection = await internetChecker.hasConnection;

    if (hasConnection) {
      try {
        final announcements = await remote.fetchAnnouncements();
        await local.saveAnnouncements(announcements);
        return announcements;
      } catch (_) {
        return await local.getAnnouncements();
      }
    } else {
      return await local.getAnnouncements();
    }
  }

  Future<List<Announcement>> getCachedAnnouncements() async {
    return local.getAnnouncements();
  }

  /// Implements stale-while-revalidate pattern:
  /// 1. Returns cached data immediately if available
  /// 2. Triggers background refresh if online
  /// 3. Returns fresh data when background refresh completes
  Stream<List<Announcement>> fetchAnnouncementsStaleWhileRevalidate() async* {
    // First, yield cached data immediately if available
    final cachedAnnouncements = await local.getAnnouncements();
    if (cachedAnnouncements.isNotEmpty) {
      yield cachedAnnouncements;
    }

    // Then attempt to fetch fresh data in the background
    final hasConnection = await internetChecker.hasConnection;
    if (hasConnection) {
      try {
        final freshAnnouncements = await remote.fetchAnnouncements();
        await local.saveAnnouncements(freshAnnouncements);
        yield freshAnnouncements;
      } catch (_) {
        // If fresh fetch fails and we haven't yielded cached data yet, yield it now
        if (cachedAnnouncements.isEmpty) {
          final fallbackAnnouncements = await local.getAnnouncements();
          yield fallbackAnnouncements;
        }
        // If we already yielded cached data, we don't need to do anything
        // The UI will continue showing the cached data
      }
    } else if (cachedAnnouncements.isEmpty) {
      // No connection and no cached data - yield empty list
      yield <Announcement>[];
    }
  }
}
