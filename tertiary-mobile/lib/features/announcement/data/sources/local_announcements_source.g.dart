// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'local_announcements_source.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$localAnnouncementsSourceHash() =>
    r'8ce02250b2a5f24a6048ad2397d4c440a19bc006';

/// See also [localAnnouncementsSource].
@ProviderFor(localAnnouncementsSource)
final localAnnouncementsSourceProvider =
    AutoDisposeProvider<LocalAnnouncementsSource>.internal(
  localAnnouncementsSource,
  name: r'localAnnouncementsSourceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$localAnnouncementsSourceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef LocalAnnouncementsSourceRef
    = AutoDisposeProviderRef<LocalAnnouncementsSource>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
