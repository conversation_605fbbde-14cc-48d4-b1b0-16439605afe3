import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:tertiary_mobile/features/courses/data/models/credit_load_limit.dart';
import 'package:tertiary_mobile/features/courses/domain/course_table_item.dart';
import 'package:tertiary_mobile/core/constants/colors.dart';

class CompactCreditSummary extends ConsumerWidget {
  final List<CreditLoadLimit> creditLoadLimits;
  final List<CourseTableItem> selectedCourses;
  final AppColors colors;

  const CompactCreditSummary({
    super.key,
    required this.creditLoadLimits,
    required this.selectedCourses,
    required this.colors,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Calculate semester-specific totals
    final semesterTotals = _calculateSemesterTotals();
    final totalSelectedCredits =
        semesterTotals.values.fold(0, (sum, credits) => sum + credits);

    // Always show detailed summary if we have credit limits, even if no courses selected
    if (creditLoadLimits.isEmpty) {
      return _buildBasicTotal(totalSelectedCredits);
    }

    return _buildDetailedSummary(semesterTotals, totalSelectedCredits);
  }

  Map<String, int> _calculateSemesterTotals() {
    final Map<String, int> semesterTotals = {};

    for (final course in selectedCourses) {
      // Use the semester information directly from the course specification
      // Each course specification has semesterAccronym and semesterTitle
      String semesterKey = 'Unknown';

      // Check if the course has semester information from the table item
      if (course.semesterAccronym?.isNotEmpty == true) {
        semesterKey = course.semesterAccronym!;
      } else if (course.semesterTitle?.isNotEmpty == true) {
        semesterKey = course.semesterTitle!;
      } else {
        // Fallback: try to get from session semesters
        final semesters = course.session?.semesters ?? [];
        if (semesters.isNotEmpty) {
          semesterKey =
              semesters.first.accronym ?? semesters.first.title ?? 'Unknown';
        }
      }

      // Initialize semester total if not exists
      if (!semesterTotals.containsKey(semesterKey)) {
        semesterTotals[semesterKey] = 0;
      }

      // Add course credit to the appropriate semester
      semesterTotals[semesterKey] =
          (semesterTotals[semesterKey] ?? 0) + course.creditUnit;
    }

    return semesterTotals;
  }

  Widget _buildBasicTotal(int totalCredits) {
    return Text(
      'Total Units: $totalCredits',
      style: TextStyle(
        color: colors.primary,
        fontWeight: FontWeight.bold,
      ),
    );
  }

  Widget _buildDetailedSummary(
      Map<String, int> semesterTotals, int totalCredits) {
    // Create a map of semester limits
    final semesterLimits = <String, CreditLoadLimit>{};
    final generalLimits = <CreditLoadLimit>[];

    for (final limit in creditLoadLimits) {
      if (limit.semesterSetting != null) {
        final semesterKey = limit.semesterSetting!.accronym ??
            limit.semesterSetting!.title ??
            'Unknown';
        semesterLimits[semesterKey] = limit;
      } else {
        generalLimits.add(limit);
      }
    }

    // Get all available semesters (from limits or from selected courses)
    final allSemesters = <String>{};
    allSemesters.addAll(semesterLimits.keys);
    allSemesters.addAll(semesterTotals.keys);

    // If no specific semester limits, create default semesters
    if (allSemesters.isEmpty && generalLimits.isNotEmpty) {
      allSemesters.addAll(['1st', '2nd']); // Default semesters
    }

    // Calculate total limits
    int totalMinLimit = 0;
    int totalMaxLimit = 0;

    if (semesterLimits.isNotEmpty) {
      totalMinLimit = semesterLimits.values
          .fold(0, (sum, limit) => sum + limit.minCreditUnit);
      totalMaxLimit = semesterLimits.values
          .fold(0, (sum, limit) => sum + limit.maxCreditUnit);
    } else if (generalLimits.isNotEmpty) {
      final generalLimit = generalLimits.first;
      totalMinLimit = generalLimit.minCreditUnit;
      totalMaxLimit = generalLimit.maxCreditUnit;
    }

    // Determine status and color based on both semester and total validation
    Color statusColor = colors.primary;
    String statusIcon = '';
    bool hasViolation = false;

    // Check individual semester violations
    for (final entry in semesterTotals.entries) {
      final semester = entry.key;
      final credits = entry.value;
      final limit = semesterLimits[semester];

      if (limit != null) {
        if (credits > limit.maxCreditUnit) {
          statusColor = Colors.red;
          statusIcon = ' ⚠️';
          hasViolation = true;
          break;
        } else if (credits < limit.minCreditUnit && credits > 0) {
          statusColor = Colors.orange;
          statusIcon = ' ⚠️';
          hasViolation = true;
        }
      }
    }

    // Check total limits if no semester violations
    if (!hasViolation && totalMaxLimit > 0) {
      if (totalCredits > totalMaxLimit) {
        statusColor = Colors.red;
        statusIcon = ' ⚠️';
      } else if (totalCredits < totalMinLimit && totalCredits > 0) {
        statusColor = Colors.orange;
        statusIcon = ' ⚠️';
      } else if (totalCredits > 0) {
        statusColor = Colors.green;
        statusIcon = ' ✓';
      }
    }

    // Calculate total limit text
    String totalLimitText = '';
    if (totalMaxLimit > 0) {
      totalLimitText = ' ($totalMinLimit-$totalMaxLimit)';
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Semester breakdown with limits
        ...allSemesters.map((semesterKey) {
          final credits = semesterTotals[semesterKey] ?? 0;
          final limit = semesterLimits[semesterKey];

          String limitText = '';
          Color semesterColor = colors.textGrey;
          String semesterIcon = '';

          if (limit != null) {
            limitText = ' (${limit.minCreditUnit}-${limit.maxCreditUnit})';

            // Color code based on semester-specific validation
            if (credits > limit.maxCreditUnit) {
              semesterColor = Colors.red;
              semesterIcon = ' ⚠️';
            } else if (credits < limit.minCreditUnit && credits > 0) {
              semesterColor = Colors.orange;
              semesterIcon = ' ⚠️';
            } else if (credits > 0) {
              semesterColor = Colors.green;
              semesterIcon = ' ✓';
            }
          }

          return Text(
            '$semesterKey: $credits units$limitText$semesterIcon',
            style: TextStyle(
              color: semesterColor,
              fontSize: 10.sp,
            ),
          );
        }),
        if (allSemesters.isNotEmpty) SizedBox(height: 2.h),

        // Total with status and combined limits
        Text(
          'Total: $totalCredits units$totalLimitText$statusIcon',
          style: TextStyle(
            color: statusColor,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }
}
