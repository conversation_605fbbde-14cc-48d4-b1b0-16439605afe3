import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:tertiary_mobile/features/courses/data/models/credit_load_limit.dart';
import 'package:tertiary_mobile/core/constants/colors.dart';

class CreditLoadSummary extends ConsumerWidget {
  final List<CreditLoadLimit> creditLoadLimits;
  final int totalSelectedCredits;

  const CreditLoadSummary({
    super.key,
    required this.creditLoadLimits,
    required this.totalSelectedCredits,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final colors = ref.watch(appColorsProvider);
    final theme = Theme.of(context);

    if (creditLoadLimits.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(8.r),
        border:
            Border.all(color: theme.colorScheme.outline.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Credit Load Summary',
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.w600,
              color: colors.primary,
            ),
          ),
          SizedBox(height: 8.h),
          _buildTotalCreditsRow(colors, theme),
          SizedBox(height: 8.h),
          ...creditLoadLimits
              .map((limit) => _buildLimitRow(limit, colors, theme)),
        ],
      ),
    );
  }

  Widget _buildTotalCreditsRow(AppColors colors, ThemeData theme) {
    // Determine credit status color
    Color statusColor = colors.primary;
    String statusText = 'Total Selected: $totalSelectedCredits units';

    // Check if we have any limits to compare against
    if (creditLoadLimits.isNotEmpty) {
      final generalLimit = creditLoadLimits.first;
      if (totalSelectedCredits > generalLimit.maxCreditUnit) {
        statusColor = Colors.red;
        statusText += ' (Exceeds limit)';
      } else if (totalSelectedCredits < generalLimit.minCreditUnit) {
        statusColor = Colors.orange;
        statusText += ' (Below minimum)';
      } else {
        statusColor = Colors.green;
        statusText += ' (Within limits)';
      }
    }

    return Text(
      statusText,
      style: TextStyle(
        fontSize: 12.sp,
        fontWeight: FontWeight.w500,
        color: statusColor,
      ),
    );
  }

  Widget _buildLimitRow(
      CreditLoadLimit limit, AppColors colors, ThemeData theme) {
    final semesterName = limit.semesterSetting?.title ??
        limit.semesterSetting?.accronym ??
        'General';

    return Padding(
      padding: EdgeInsets.only(bottom: 4.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            semesterName,
            style: TextStyle(
              fontSize: 11.sp,
              color: theme.colorScheme.onSurface.withValues(alpha: 0.8),
            ),
          ),
          Text(
            'Min: ${limit.minCreditUnit} | Max: ${limit.maxCreditUnit}',
            style: TextStyle(
              fontSize: 11.sp,
              color: theme.colorScheme.onSurface.withValues(alpha: 0.8),
            ),
          ),
        ],
      ),
    );
  }
}
