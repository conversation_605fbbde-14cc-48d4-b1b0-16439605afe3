// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'course_registration_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$groupedCourseSpecificationsHash() =>
    r'aae8c36d11a7a9452ab41f0d4bdc32c9638f5ead';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [groupedCourseSpecifications].
@ProviderFor(groupedCourseSpecifications)
const groupedCourseSpecificationsProvider = GroupedCourseSpecificationsFamily();

/// See also [groupedCourseSpecifications].
class GroupedCourseSpecificationsFamily
    extends Family<AsyncValue<Map<String, List<CourseTableItem>>>> {
  /// See also [groupedCourseSpecifications].
  const GroupedCourseSpecificationsFamily();

  /// See also [groupedCourseSpecifications].
  GroupedCourseSpecificationsProvider call(
    int? sessionId,
  ) {
    return GroupedCourseSpecificationsProvider(
      sessionId,
    );
  }

  @override
  GroupedCourseSpecificationsProvider getProviderOverride(
    covariant GroupedCourseSpecificationsProvider provider,
  ) {
    return call(
      provider.sessionId,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'groupedCourseSpecificationsProvider';
}

/// See also [groupedCourseSpecifications].
class GroupedCourseSpecificationsProvider
    extends AutoDisposeFutureProvider<Map<String, List<CourseTableItem>>> {
  /// See also [groupedCourseSpecifications].
  GroupedCourseSpecificationsProvider(
    int? sessionId,
  ) : this._internal(
          (ref) => groupedCourseSpecifications(
            ref as GroupedCourseSpecificationsRef,
            sessionId,
          ),
          from: groupedCourseSpecificationsProvider,
          name: r'groupedCourseSpecificationsProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$groupedCourseSpecificationsHash,
          dependencies: GroupedCourseSpecificationsFamily._dependencies,
          allTransitiveDependencies:
              GroupedCourseSpecificationsFamily._allTransitiveDependencies,
          sessionId: sessionId,
        );

  GroupedCourseSpecificationsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.sessionId,
  }) : super.internal();

  final int? sessionId;

  @override
  Override overrideWith(
    FutureOr<Map<String, List<CourseTableItem>>> Function(
            GroupedCourseSpecificationsRef provider)
        create,
  ) {
    return ProviderOverride(
      origin: this,
      override: GroupedCourseSpecificationsProvider._internal(
        (ref) => create(ref as GroupedCourseSpecificationsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        sessionId: sessionId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<Map<String, List<CourseTableItem>>>
      createElement() {
    return _GroupedCourseSpecificationsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is GroupedCourseSpecificationsProvider &&
        other.sessionId == sessionId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, sessionId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin GroupedCourseSpecificationsRef
    on AutoDisposeFutureProviderRef<Map<String, List<CourseTableItem>>> {
  /// The parameter `sessionId` of this provider.
  int? get sessionId;
}

class _GroupedCourseSpecificationsProviderElement
    extends AutoDisposeFutureProviderElement<Map<String, List<CourseTableItem>>>
    with GroupedCourseSpecificationsRef {
  _GroupedCourseSpecificationsProviderElement(super.provider);

  @override
  int? get sessionId =>
      (origin as GroupedCourseSpecificationsProvider).sessionId;
}

String _$courseRegistrationNotifierHash() =>
    r'402706ddf07596fea8712083ba4431e35acd5524';

abstract class _$CourseRegistrationNotifier
    extends BuildlessAutoDisposeAsyncNotifier<CourseRegistrationState> {
  late final int? sessionId;

  FutureOr<CourseRegistrationState> build([
    int? sessionId,
  ]);
}

/// See also [CourseRegistrationNotifier].
@ProviderFor(CourseRegistrationNotifier)
const courseRegistrationNotifierProvider = CourseRegistrationNotifierFamily();

/// See also [CourseRegistrationNotifier].
class CourseRegistrationNotifierFamily
    extends Family<AsyncValue<CourseRegistrationState>> {
  /// See also [CourseRegistrationNotifier].
  const CourseRegistrationNotifierFamily();

  /// See also [CourseRegistrationNotifier].
  CourseRegistrationNotifierProvider call([
    int? sessionId,
  ]) {
    return CourseRegistrationNotifierProvider(
      sessionId,
    );
  }

  @override
  CourseRegistrationNotifierProvider getProviderOverride(
    covariant CourseRegistrationNotifierProvider provider,
  ) {
    return call(
      provider.sessionId,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'courseRegistrationNotifierProvider';
}

/// See also [CourseRegistrationNotifier].
class CourseRegistrationNotifierProvider
    extends AutoDisposeAsyncNotifierProviderImpl<CourseRegistrationNotifier,
        CourseRegistrationState> {
  /// See also [CourseRegistrationNotifier].
  CourseRegistrationNotifierProvider([
    int? sessionId,
  ]) : this._internal(
          () => CourseRegistrationNotifier()..sessionId = sessionId,
          from: courseRegistrationNotifierProvider,
          name: r'courseRegistrationNotifierProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$courseRegistrationNotifierHash,
          dependencies: CourseRegistrationNotifierFamily._dependencies,
          allTransitiveDependencies:
              CourseRegistrationNotifierFamily._allTransitiveDependencies,
          sessionId: sessionId,
        );

  CourseRegistrationNotifierProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.sessionId,
  }) : super.internal();

  final int? sessionId;

  @override
  FutureOr<CourseRegistrationState> runNotifierBuild(
    covariant CourseRegistrationNotifier notifier,
  ) {
    return notifier.build(
      sessionId,
    );
  }

  @override
  Override overrideWith(CourseRegistrationNotifier Function() create) {
    return ProviderOverride(
      origin: this,
      override: CourseRegistrationNotifierProvider._internal(
        () => create()..sessionId = sessionId,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        sessionId: sessionId,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<CourseRegistrationNotifier,
      CourseRegistrationState> createElement() {
    return _CourseRegistrationNotifierProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is CourseRegistrationNotifierProvider &&
        other.sessionId == sessionId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, sessionId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin CourseRegistrationNotifierRef
    on AutoDisposeAsyncNotifierProviderRef<CourseRegistrationState> {
  /// The parameter `sessionId` of this provider.
  int? get sessionId;
}

class _CourseRegistrationNotifierProviderElement
    extends AutoDisposeAsyncNotifierProviderElement<CourseRegistrationNotifier,
        CourseRegistrationState> with CourseRegistrationNotifierRef {
  _CourseRegistrationNotifierProviderElement(super.provider);

  @override
  int? get sessionId =>
      (origin as CourseRegistrationNotifierProvider).sessionId;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
