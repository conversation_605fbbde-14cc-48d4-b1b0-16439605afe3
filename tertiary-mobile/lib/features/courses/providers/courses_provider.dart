import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:tertiary_mobile/features/courses/data/models/course_registration_model.dart';
import 'package:tertiary_mobile/features/courses/domain/course_table_item.dart';
import '../data/repositories/courses_repository.dart';

part 'courses_provider.g.dart';

@riverpod
class CoursesNotifier extends _$CoursesNotifier {
  @override
  Future<List<CourseRegistration>> build(int? sessionId) async {
    // If no session is selected, return empty list
    if (sessionId == null) {
      return [];
    }

    final repository = ref.read(coursesRepositoryProvider);
    return await repository.fetchCourseRegistrations(sessionId);
  }

  Future<void> refresh(int? sessionId) async {
    // For manual refresh, show loading state and fetch fresh data
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(
      () async {
        final repository = ref.read(coursesRepositoryProvider);
        return await repository.fetchCourseRegistrations(sessionId);
      },
    );
  }
}

@riverpod
Future<Map<String, List<CourseTableItem>>> groupedCourseRegistrations(
  Ref ref,
  int? sessionId,
) async {
  ref.keepAlive();
  final regState = await ref.watch(coursesNotifierProvider(sessionId).future);
  // Group registrations by semester title or semester accronym
  final Map<String, List<CourseTableItem>> grouped = {};
  for (final reg in regState) {
    final sem = reg.semesterTitle ?? reg.semesterAccronym ?? 'Semester';
    final tableItem = reg.toTableItem();
    grouped.putIfAbsent(sem, () => []).add(tableItem);
  }
  return grouped;
}
