import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:tertiary_mobile/core/utils/logger.dart';
import 'package:tertiary_mobile/features/courses/domain/course_table_item.dart';
import 'package:tertiary_mobile/features/courses/data/repositories/courses_repository.dart';
import 'package:tertiary_mobile/features/courses/data/models/credit_load_limit.dart';

part 'course_registration_provider.g.dart';

class CourseRegistrationState {
  final List<CourseTableItem> selected;
  final List<CourseTableItem> specifications;
  final List<CreditLoadLimit> creditLoadLimits;
  final bool isSubmitting;
  final String? submissionError;

  CourseRegistrationState({
    this.selected = const [],
    this.specifications = const [],
    this.creditLoadLimits = const [],
    this.isSubmitting = false,
    this.submissionError,
  });

  CourseRegistrationState copyWith({
    List<CourseTableItem>? selected,
    List<CourseTableItem>? specifications,
    List<CreditLoadLimit>? creditLoadLimits,
    bool? isSubmitting,
    String? submissionError,
  }) {
    return CourseRegistrationState(
      selected: selected ?? this.selected,
      specifications: specifications ?? this.specifications,
      creditLoadLimits: creditLoadLimits ?? this.creditLoadLimits,
      isSubmitting: isSubmitting ?? this.isSubmitting,
      submissionError: submissionError,
    );
  }

  // Helper methods for credit calculations
  int getTotalSelectedCredits() {
    return selected.fold(0, (sum, course) => sum + course.creditUnit);
  }

  int getMaxCreditForSemester(String? semesterAcronym) {
    if (semesterAcronym == null) return 0;
    final limit = creditLoadLimits.firstWhere(
      (limit) => limit.semesterSetting?.accronym == semesterAcronym,
      orElse: () => creditLoadLimits.firstWhere(
        (limit) => limit.semesterSettingId == null, // General limit
        orElse: () => const CreditLoadLimit(
          id: 0,
          maxCreditUnit: 0,
          maxExtraCreditUnit: 0,
          minCreditUnit: 0,
          programmeId: 0,
          studentAcademicLevelId: 0,
        ),
      ),
    );
    return limit.maxCreditUnit;
  }

  int getMinCreditForSemester(String? semesterAcronym) {
    if (semesterAcronym == null) return 0;
    final limit = creditLoadLimits.firstWhere(
      (limit) => limit.semesterSetting?.accronym == semesterAcronym,
      orElse: () => creditLoadLimits.firstWhere(
        (limit) => limit.semesterSettingId == null, // General limit
        orElse: () => const CreditLoadLimit(
          id: 0,
          maxCreditUnit: 0,
          maxExtraCreditUnit: 0,
          minCreditUnit: 0,
          programmeId: 0,
          studentAcademicLevelId: 0,
        ),
      ),
    );
    return limit.minCreditUnit;
  }

  // Get total maximum credit (sum of all semester maximums)
  int getTotalMaxCredit() {
    if (creditLoadLimits.isEmpty) return 0;

    // If we have semester-specific limits, sum them up
    final semesterSpecificLimits =
        creditLoadLimits.where((limit) => limit.semesterSetting != null);
    if (semesterSpecificLimits.isNotEmpty) {
      return semesterSpecificLimits.fold(
          0, (sum, limit) => sum + limit.maxCreditUnit);
    }

    // Otherwise use general limit
    return creditLoadLimits.first.maxCreditUnit;
  }

  // Get total minimum credit (sum of all semester minimums)
  int getTotalMinCredit() {
    if (creditLoadLimits.isEmpty) return 0;

    // If we have semester-specific limits, sum them up
    final semesterSpecificLimits =
        creditLoadLimits.where((limit) => limit.semesterSetting != null);
    if (semesterSpecificLimits.isNotEmpty) {
      return semesterSpecificLimits.fold(
          0, (sum, limit) => sum + limit.minCreditUnit);
    }

    // Otherwise use general limit
    return creditLoadLimits.first.minCreditUnit;
  }
}

@riverpod
class CourseRegistrationNotifier extends _$CourseRegistrationNotifier {
  @override
  Future<CourseRegistrationState> build([int? sessionId]) async {
    if (sessionId == null) {
      return CourseRegistrationState(
        selected: [],
        specifications: [],
      );
    }

    final repository = ref.read(coursesRepositoryProvider);

    // Get the current state to check for selected courses
    final currentState = state.valueOrNull;
    final selectedCourseIds =
        currentState?.selected.map((c) => c.sourceId).toSet() ?? <String>{};

    try {
      final response =
          await repository.fetchCourseSpecificationsWithCreditLimits(sessionId);

      final tableItems = response.courseSpecifications.map((spec) {
        final item = spec.toTableItem();
        final isSelected =
            selectedCourseIds.any((id) => id == spec.id.toString());
        return isSelected ? item.copyWith(isRegistered: true) : item;
      }).toList();

      return CourseRegistrationState(
        selected: currentState?.selected ?? [],
        specifications: tableItems,
        creditLoadLimits: response.creditLoadLimits,
      );
    } catch (_) {
      // Fallback: try to get just specifications without credit limits
      try {
        final specs = await repository.fetchCourseSpecifications(sessionId);
        final tableItems = specs.map((spec) {
          final item = spec.toTableItem();
          final isSelected = selectedCourseIds
              .any((id) => id.toString() == spec.id.toString());
          return isSelected ? item.copyWith(isRegistered: true) : item;
        }).toList();

        return CourseRegistrationState(
          selected: currentState?.selected ?? [],
          specifications: tableItems,
          creditLoadLimits: [],
        );
      } catch (_) {
        // Final fallback: return empty state
        return CourseRegistrationState(
          selected: currentState?.selected ?? [],
          specifications: [],
          creditLoadLimits: [],
        );
      }
    }
  }

  void addCourse(CourseTableItem item) {
    final current = state.value;
    logger.i('Adding course: ${item.courseCode} (${item.sourceId})');
    if (current == null) {
      logger.e('Current state is null, cannot add course');
      return;
    }

    final before = current.selected;
    if (before.any((c) => c.id == item.id)) {
      logger.i('Course already selected, skipping');
      return;
    }

    // Check credit limit validation
    final courseCredits = item.creditUnit;
    final courseSemester = item.semesterAccronym ?? item.semesterTitle;

    // Validate semester-specific limits
    if (courseSemester != null) {
      final currentSemesterCredits = current.selected
          .where(
              (c) => (c.semesterAccronym ?? c.semesterTitle) == courseSemester)
          .fold(0, (sum, c) => sum + c.creditUnit);

      final newSemesterCredits = currentSemesterCredits + courseCredits;
      final maxSemesterCredit = current.getMaxCreditForSemester(courseSemester);

      if (maxSemesterCredit > 0 && newSemesterCredits > maxSemesterCredit) {
        logger.w(
            'Cannot add course: would exceed $courseSemester semester maximum ($maxSemesterCredit)');
        throw Exception(
            'Cannot add course: $courseSemester semester maximum is $maxSemesterCredit units. Adding this course would result in $newSemesterCredits units for this semester.');
      }
    }

    // Validate total limits (combined from all semesters)
    final newTotalCredits = current.getTotalSelectedCredits() + courseCredits;
    final totalMaxCredit = current.getTotalMaxCredit();

    if (totalMaxCredit > 0 && newTotalCredits > totalMaxCredit) {
      logger.w(
          'Cannot add course: would exceed total maximum credit limit ($totalMaxCredit)');
      throw Exception(
          'Cannot add course: Total maximum credit limit is $totalMaxCredit units. Adding this course would result in $newTotalCredits units.');
    }

    // Mark the item as registered
    final updatedItem = item.copyWith(isRegistered: true);

    // Update the specifications to reflect the change
    final updatedSpecs = current.specifications.map((spec) {
      return spec.id == item.id ? updatedItem : spec;
    }).toList();

    final newSelected = [...before, updatedItem];
    logger.i('New selected count: ${newSelected.length}');

    state = AsyncValue.data(
      current.copyWith(
        selected: newSelected,
        specifications: updatedSpecs,
      ),
    );
  }

  void removeCourse(CourseTableItem item) {
    final current = state.value;
    if (current == null) return;

    final before = current.selected;
    final filtered = before.where((c) => c.id != item.id).toList();

    // Mark the item as not registered in specifications
    final updatedSpecs = current.specifications.map((spec) {
      return spec.id == item.id ? spec.copyWith(isRegistered: false) : spec;
    }).toList();

    state = AsyncValue.data(
      current.copyWith(
        selected: filtered,
        specifications: updatedSpecs,
      ),
    );
  }

  void syncSelectedCourses(List<CourseTableItem> selectedCourses) {
    final current = state.value;
    if (current == null) {
      logger.e(
          'Provider state is null, creating new state with selected courses');
      // Create a new state if current is null
      state = AsyncValue.data(CourseRegistrationState(
        selected: selectedCourses,
        specifications: [],
      ));
      return;
    }

    logger.i('Syncing selected courses: ${selectedCourses.length}');

    // Update the state to match the provided selected courses
    state = AsyncValue.data(current.copyWith(
      selected: selectedCourses,
    ));
  }

  Future<void> submitRegistration() async {
    final current = state.value;
    logger.i('Submitting registration state: $current');
    if (current == null) {
      logger.e('Provider state is null, cannot submit registration');
      throw Exception('Provider state is not initialized. Please try again.');
    }
    if (current.isSubmitting) {
      logger.i('Already submitting, skipping');
      return;
    }
    if (current.selected.isEmpty) {
      logger.e('No courses selected for registration');
      throw Exception('No courses selected for registration');
    }

    // Check minimum credit requirements (both per-semester and total)
    final totalCredits = current.getTotalSelectedCredits();
    final totalMinCredit = current.getTotalMinCredit();

    // Check total minimum requirement
    if (totalMinCredit > 0 && totalCredits < totalMinCredit) {
      logger.e(
          'Total minimum credit requirement not met: $totalCredits < $totalMinCredit');
      throw Exception(
          'Total minimum credit requirement not met. You need at least $totalMinCredit units but have selected $totalCredits units.');
    }

    // Check per-semester minimum requirements
    final semesterGroups = <String, List<CourseTableItem>>{};
    for (final course in current.selected) {
      final semester =
          course.semesterAccronym ?? course.semesterTitle ?? 'Unknown';
      semesterGroups[semester] = (semesterGroups[semester] ?? [])..add(course);
    }

    for (final entry in semesterGroups.entries) {
      final semester = entry.key;
      final courses = entry.value;
      final semesterCredits = courses.fold(0, (sum, c) => sum + c.creditUnit);
      final minSemesterCredit = current.getMinCreditForSemester(semester);

      if (minSemesterCredit > 0 && semesterCredits < minSemesterCredit) {
        logger.e(
            '$semester semester minimum not met: $semesterCredits < $minSemesterCredit');
        throw Exception(
            '$semester semester minimum requirement not met. You need at least $minSemesterCredit units for $semester semester but have selected $semesterCredits units.');
      }
    }

    logger.i('Selected courses count: ${current.selected.length}');
    logger.i(
        'Selected courses: ${current.selected.map((c) => '${c.courseCode} (${c.sourceId})').toList()}');

    try {
      // Set submitting state
      state = AsyncValue.data(current.copyWith(
        isSubmitting: true,
        submissionError: null,
      ));

      // Transform selected courses to the required format
      final registrations = current.selected
          .map((item) => {
                'course_id': int.tryParse(item.sourceId) ?? 0,
                'semester_id': item.session?.semesters.isNotEmpty == true
                    ? item.session!.semesters.first.id
                    : null,
                'session_id': item.session?.id,
              })
          .where((reg) => reg['course_id'] != 0 && reg['semester_id'] != null)
          .toList();
      logger.e('Final registrations array: $registrations');
      // Call repository
      await ref
          .read(coursesRepositoryProvider)
          .submitCourseRegistrations(registrations);

      // Clear selected courses on success
      state = AsyncValue.data(current.copyWith(
        selected: [],
        isSubmitting: false,
      ));
    } catch (e) {
      state = AsyncValue.data(current.copyWith(
        isSubmitting: false,
        submissionError: e.toString(),
      ));
      rethrow;
    }
  }

  Future<void> refresh(int? sessionId) async {
    if (sessionId == null) {
      state = AsyncValue.data(CourseRegistrationState());
      return;
    }

    // For manual refresh, show loading state and fetch fresh data
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      final repository = ref.read(coursesRepositoryProvider);
      final response =
          await repository.fetchCourseSpecificationsWithCreditLimits(sessionId);

      // Get current state to preserve selected courses
      final currentState = state.valueOrNull;
      final selectedCourseIds =
          currentState?.selected.map((c) => c.sourceId).toSet() ?? <String>{};

      // Convert specs to table items and set isRegistered based on selected courses
      final tableItems = response.courseSpecifications.map((spec) {
        final item = spec.toTableItem();
        final isSelected =
            selectedCourseIds.any((id) => id == spec.id.toString());
        return isSelected ? item.copyWith(isRegistered: true) : item;
      }).toList();

      return CourseRegistrationState(
        selected: currentState?.selected ?? [],
        specifications: tableItems,
        creditLoadLimits: response.creditLoadLimits,
      );
    });
  }
}

@riverpod
Future<Map<String, List<CourseTableItem>>> groupedCourseSpecifications(
  Ref ref,
  int? sessionId,
) async {
  ref.keepAlive();
  final state =
      await ref.watch(courseRegistrationNotifierProvider(sessionId).future);

  final Map<String, List<CourseTableItem>> grouped = {};
  for (final spec in state.specifications) {
    final semesters = spec.session?.semesters ?? [];
    final sem = semesters.isNotEmpty
        ? (semesters.first.title ?? semesters.first.accronym ?? 'Semester')
        : 'Semester';
    grouped.putIfAbsent(sem, () => []).add(spec);
  }

  return grouped;
}
