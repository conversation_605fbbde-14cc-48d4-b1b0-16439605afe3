// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'selected_session_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$selectedSessionHash() => r'8d1a379540f842a1c3dba1f5fc3924be0d07dba6';

/// See also [SelectedSession].
@ProviderFor(SelectedSession)
final selectedSessionProvider =
    AutoDisposeNotifierProvider<SelectedSession, int?>.internal(
  SelectedSession.new,
  name: r'selectedSessionProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$selectedSessionHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SelectedSession = AutoDisposeNotifier<int?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
