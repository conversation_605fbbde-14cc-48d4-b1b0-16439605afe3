import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:tertiary_mobile/core/utils/shared_preferences.dart';
import 'package:tertiary_mobile/features/authentication/providers/auth_provider.dart';

part 'selected_session_provider.g.dart';

@riverpod
class SelectedSession extends _$SelectedSession {
  static const String _selectedSessionKey = 'selected_session_id';

  @override
  int? build() {
    // Load the last selected session from storage
    _loadSelectedSession();
    return null;
  }

  Future<void> _loadSelectedSession() async {
    try {
      final sessionId = SharedPreferencesService.getInt(_selectedSessionKey);
      if (sessionId != null) {
        state = sessionId;
      } else {
        // If no stored session, try to use user's current session
        final user = await ref.read(authProvider.future);
        if (user?.currentAcademicSessionWithSemesters != null) {
          final currentSessionId =
              user!.currentAcademicSessionWithSemesters!.id;
          state = currentSessionId;
          // Save this as the default for future use
          await SharedPreferencesService.setInt(
              _selectedSessionKey, currentSessionId);
        }
      }
    } catch (e) {
      // Ignore errors and use default null state
    }
  }

  Future<void> setSession(int? sessionId) async {
    state = sessionId;

    // Persist the selected session
    try {
      if (sessionId != null) {
        await SharedPreferencesService.setInt(_selectedSessionKey, sessionId);
      } else {
        await SharedPreferencesService.remove(_selectedSessionKey);
      }
    } catch (e) {
      // Ignore storage errors, the session is still set in memory
    }
  }
}
