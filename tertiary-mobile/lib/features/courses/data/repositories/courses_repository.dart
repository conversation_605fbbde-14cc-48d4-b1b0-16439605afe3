import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:tertiary_mobile/core/network/providers/internet_checker.dart';
import 'package:tertiary_mobile/features/courses/data/models/course_specification_model.dart';
import 'package:tertiary_mobile/features/courses/data/models/course_specification_response.dart';
import 'package:tertiary_mobile/features/courses/data/models/credit_load_limit.dart';
import '../models/course_registration_model.dart';
import '../sources/remote_courses_source.dart';
import '../sources/local_courses_source.dart';
import '../sources/local_course_specifications_source.dart';
import '../sources/local_credit_load_limits_source.dart';

part 'courses_repository.g.dart';

@riverpod
CoursesRepository coursesRepository(Ref ref) {
  final remote = ref.read(remoteCoursesSourceProvider);
  final localRegistrations = ref.read(localCourseRegistrationsSourceProvider);
  final localSpecs = ref.read(localCourseSpecificationsSourceProvider);
  final localCreditLimits = ref.read(localCreditLoadLimitsSourceProvider);
  final internetChecker = ref.read(internetCheckerProvider);
  return CoursesRepository(remote, localRegistrations, localSpecs,
      localCreditLimits, internetChecker);
}

class CoursesRepository {
  final RemoteCoursesSource remote;
  final LocalCourseRegistrationsSource localRegistrations;
  final LocalCourseSpecificationsSource localSpecs;
  final LocalCreditLoadLimitsSource localCreditLimits;
  final InternetConnectionChecker internetChecker;

  CoursesRepository(
    this.remote,
    this.localRegistrations,
    this.localSpecs,
    this.localCreditLimits,
    this.internetChecker,
  );

  Future<List<CourseRegistration>> fetchCourseRegistrations(
      int? sessionId) async {
    final hasConnection = await internetChecker.hasConnection;

    if (hasConnection) {
      try {
        final regs = await remote.fetchCourseRegistrations(sessionId);
        await localRegistrations.saveCourseRegistrations(regs);
        return regs;
      } catch (_) {
        return await localRegistrations.getCourseRegistrations(sessionId);
      }
    } else {
      return await localRegistrations.getCourseRegistrations(sessionId);
    }
  }

  Future<List<CourseSpecification>> fetchCourseSpecifications(
      int? sessionId) async {
    final hasConnection = await internetChecker.hasConnection;
    if (hasConnection) {
      try {
        final specs = await remote.fetchCourseSpecifications(sessionId);
        await localSpecs.saveCourseSpecifications(specs);
        return specs;
      } catch (_) {
        return await localSpecs.getCourseSpecifications(sessionId);
      }
    } else {
      return await localSpecs.getCourseSpecifications(sessionId);
    }
  }

  Future<CourseSpecificationResponse> fetchCourseSpecificationsWithCreditLimits(
      int? sessionId) async {
    final hasConnection = await internetChecker.hasConnection;
    if (hasConnection) {
      try {
        final response =
            await remote.fetchCourseSpecificationsWithCreditLimits(sessionId);
        // Save both course specifications and credit limits to local cache
        await localSpecs
            .saveCourseSpecifications(response.courseSpecifications);
        await localCreditLimits.saveCreditLoadLimits(response.creditLoadLimits);
        return response;
      } catch (_) {
        // Fallback to cached data with cached credit limits
        final cachedSpecs = await localSpecs.getCourseSpecifications(sessionId);
        final cachedLimits = await localCreditLimits.getCreditLoadLimits();
        return CourseSpecificationResponse(
          sessionId: sessionId ?? 0,
          sessionName: '',
          courseSpecifications: cachedSpecs,
          creditLoadLimits: cachedLimits,
        );
      }
    } else {
      // Offline mode - return cached data with cached credit limits
      final cachedSpecs = await localSpecs.getCourseSpecifications(sessionId);
      final cachedLimits = await localCreditLimits.getCreditLoadLimits();
      return CourseSpecificationResponse(
        sessionId: sessionId ?? 0,
        sessionName: '',
        courseSpecifications: cachedSpecs,
        creditLoadLimits: cachedLimits,
      );
    }
  }

  /// Implements stale-while-revalidate pattern for course specifications with credit limits:
  /// 1. Returns cached data immediately if available
  /// 2. Triggers background refresh if online
  /// 3. Returns fresh data when background refresh completes
  Stream<CourseSpecificationResponse>
      fetchCourseSpecificationsWithCreditLimitsStaleWhileRevalidate(
          int? sessionId) async* {
    // First, yield cached data immediately if available
    final cachedSpecs = await localSpecs.getCourseSpecifications(sessionId);
    final cachedLimits = await localCreditLimits.getCreditLoadLimits();

    if (cachedSpecs.isNotEmpty || cachedLimits.isNotEmpty) {
      yield CourseSpecificationResponse(
        sessionId: sessionId ?? 0,
        sessionName: '',
        courseSpecifications: cachedSpecs,
        creditLoadLimits: cachedLimits,
      );
    }

    // Then attempt to fetch fresh data in the background
    final hasConnection = await internetChecker.hasConnection;
    if (hasConnection) {
      try {
        final freshResponse =
            await remote.fetchCourseSpecificationsWithCreditLimits(sessionId);
        await localSpecs
            .saveCourseSpecifications(freshResponse.courseSpecifications);
        await localCreditLimits
            .saveCreditLoadLimits(freshResponse.creditLoadLimits);
        yield freshResponse;
      } catch (_) {
        // If fresh fetch fails and we haven't yielded cached data yet, yield it now
        if (cachedSpecs.isEmpty && cachedLimits.isEmpty) {
          final fallbackSpecs =
              await localSpecs.getCourseSpecifications(sessionId);
          final fallbackLimits = await localCreditLimits.getCreditLoadLimits();
          yield CourseSpecificationResponse(
            sessionId: sessionId ?? 0,
            sessionName: '',
            courseSpecifications: fallbackSpecs,
            creditLoadLimits: fallbackLimits,
          );
        }
        // If we already yielded cached data, we don't need to do anything
        // The UI will continue showing the cached data
      }
    } else if (cachedSpecs.isEmpty && cachedLimits.isEmpty) {
      // No connection and no cached data - yield empty response
      yield CourseSpecificationResponse(
        sessionId: sessionId ?? 0,
        sessionName: '',
        courseSpecifications: [],
        creditLoadLimits: [],
      );
    }
  }

  Future<List<CourseRegistration>> getCachedCourseRegistrations(
      int? sessionId) async {
    return localRegistrations.getCourseRegistrations(sessionId);
  }

  Future<List<CourseSpecification>> getCachedCourseSpecifications(
      int? sessionId) async {
    return localSpecs.getCourseSpecifications(sessionId);
  }

  Future<List<CreditLoadLimit>> getCachedCreditLoadLimits() async {
    return localCreditLimits.getCreditLoadLimits();
  }

  /// Implements stale-while-revalidate pattern for course registrations:
  /// 1. Returns cached data immediately if available
  /// 2. Triggers background refresh if online
  /// 3. Returns fresh data when background refresh completes
  Stream<List<CourseRegistration>> fetchCourseRegistrationsStaleWhileRevalidate(
      int? sessionId) async* {
    // First, yield cached data immediately if available
    final cachedRegistrations =
        await localRegistrations.getCourseRegistrations(sessionId);
    if (cachedRegistrations.isNotEmpty) {
      yield cachedRegistrations;
    }

    // Then attempt to fetch fresh data in the background
    final hasConnection = await internetChecker.hasConnection;
    if (hasConnection) {
      try {
        final freshRegistrations =
            await remote.fetchCourseRegistrations(sessionId);
        await localRegistrations.saveCourseRegistrations(freshRegistrations);
        yield freshRegistrations;
      } catch (_) {
        // If fresh fetch fails and we haven't yielded cached data yet, yield it now
        if (cachedRegistrations.isEmpty) {
          final fallbackRegistrations =
              await localRegistrations.getCourseRegistrations(sessionId);
          yield fallbackRegistrations;
        }
        // If we already yielded cached data, we don't need to do anything
        // The UI will continue showing the cached data
      }
    } else if (cachedRegistrations.isEmpty) {
      // No connection and no cached data - yield empty list
      yield <CourseRegistration>[];
    }
  }

  /// Implements stale-while-revalidate pattern for course specifications:
  /// 1. Returns cached data immediately if available
  /// 2. Triggers background refresh if online
  /// 3. Returns fresh data when background refresh completes
  Stream<List<CourseSpecification>>
      fetchCourseSpecificationsStaleWhileRevalidate(int? sessionId) async* {
    // First, yield cached data immediately if available
    final cachedSpecifications =
        await localSpecs.getCourseSpecifications(sessionId);
    if (cachedSpecifications.isNotEmpty) {
      yield cachedSpecifications;
    }

    // Then attempt to fetch fresh data in the background
    final hasConnection = await internetChecker.hasConnection;
    if (hasConnection) {
      try {
        final freshSpecifications =
            await remote.fetchCourseSpecifications(sessionId);
        await localSpecs.saveCourseSpecifications(freshSpecifications);
        yield freshSpecifications;
      } catch (_) {
        // If fresh fetch fails and we haven't yielded cached data yet, yield it now
        if (cachedSpecifications.isEmpty) {
          final fallbackSpecifications =
              await localSpecs.getCourseSpecifications(sessionId);
          yield fallbackSpecifications;
        }
        // If we already yielded cached data, we don't need to do anything
        // The UI will continue showing the cached data
      }
    } else if (cachedSpecifications.isEmpty) {
      // No connection and no cached data - yield empty list
      yield <CourseSpecification>[];
    }
  }

  Future<void> submitCourseRegistrations(
      List<Map<String, dynamic>> registrations) async {
    final hasConnection = await internetChecker.hasConnection;
    if (!hasConnection) {
      throw Exception('No internet connection');
    }

    await remote.submitCourseRegistrations(registrations);
  }
}
