// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'courses_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$coursesRepositoryHash() => r'cc5dd5bea70cd3e64e7fda5aa1b4f8672c1b6350';

/// See also [coursesRepository].
@ProviderFor(coursesRepository)
final coursesRepositoryProvider =
    AutoDisposeProvider<CoursesRepository>.internal(
  coursesRepository,
  name: r'coursesRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$coursesRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CoursesRepositoryRef = AutoDisposeProviderRef<CoursesRepository>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
