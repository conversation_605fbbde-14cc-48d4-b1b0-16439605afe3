// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'local_courses_source.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$localCourseRegistrationsSourceHash() =>
    r'25cc615573400e21faeff7c73a4a3fe575ac63e1';

/// See also [localCourseRegistrationsSource].
@ProviderFor(localCourseRegistrationsSource)
final localCourseRegistrationsSourceProvider =
    AutoDisposeProvider<LocalCourseRegistrationsSource>.internal(
  localCourseRegistrationsSource,
  name: r'localCourseRegistrationsSourceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$localCourseRegistrationsSourceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef LocalCourseRegistrationsSourceRef
    = AutoDisposeProviderRef<LocalCourseRegistrationsSource>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
