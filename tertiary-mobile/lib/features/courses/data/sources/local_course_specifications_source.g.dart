// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'local_course_specifications_source.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$localCourseSpecificationsSourceHash() =>
    r'24dc758069af7eecce7ad0691bbe7b4c64e94c63';

/// See also [localCourseSpecificationsSource].
@ProviderFor(localCourseSpecificationsSource)
final localCourseSpecificationsSourceProvider =
    AutoDisposeProvider<LocalCourseSpecificationsSource>.internal(
  localCourseSpecificationsSource,
  name: r'localCourseSpecificationsSourceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$localCourseSpecificationsSourceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef LocalCourseSpecificationsSourceRef
    = AutoDisposeProviderRef<LocalCourseSpecificationsSource>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
