// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'local_credit_load_limits_source.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$localCreditLoadLimitsSourceHash() =>
    r'322be9a87b2cb77f75bbeceabe858a834957e806';

/// See also [localCreditLoadLimitsSource].
@ProviderFor(localCreditLoadLimitsSource)
final localCreditLoadLimitsSourceProvider =
    AutoDisposeProvider<LocalCreditLoadLimitsSource>.internal(
  localCreditLoadLimitsSource,
  name: r'localCreditLoadLimitsSourceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$localCreditLoadLimitsSourceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef LocalCreditLoadLimitsSourceRef
    = AutoDisposeProviderRef<LocalCreditLoadLimitsSource>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
