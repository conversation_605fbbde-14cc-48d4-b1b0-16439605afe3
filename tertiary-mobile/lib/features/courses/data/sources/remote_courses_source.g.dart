// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'remote_courses_source.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$remoteCoursesSourceHash() =>
    r'2c4e67c86ced8a3529509b17d557c254f0f8d850';

/// See also [RemoteCoursesSource].
@ProviderFor(RemoteCoursesSource)
final remoteCoursesSourceProvider = AutoDisposeNotifierProvider<
    RemoteCoursesSource, RemoteCoursesSource>.internal(
  RemoteCoursesSource.new,
  name: r'remoteCoursesSourceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$remoteCoursesSourceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$RemoteCoursesSource = AutoDisposeNotifier<RemoteCoursesSource>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
