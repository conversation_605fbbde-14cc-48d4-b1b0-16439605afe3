import 'package:drift/drift.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:tertiary_mobile/core/utils/local_database.dart';
import 'package:tertiary_mobile/core/utils/logger.dart';
import 'package:tertiary_mobile/features/courses/data/models/course_specification_model.dart';
import 'package:tertiary_mobile/features/courses/data/models/course_model.dart';
import 'package:tertiary_mobile/shared/data/models/session_semester_model.dart';

part 'local_course_specifications_source.g.dart';

@riverpod
LocalCourseSpecificationsSource localCourseSpecificationsSource(Ref ref) {
  final db = ref.read(localDatabaseProvider);
  return LocalCourseSpecificationsSource(db);
}

class LocalCourseSpecificationsSource {
  final LocalDatabase _db;

  LocalCourseSpecificationsSource(this._db);

  Future<void> saveCourseSpecifications(List<CourseSpecification> specs) async {
    try {
      await _db.batch((batch) {
        // Use upsert instead of delete-all-insert-all
        for (final spec in specs) {
          batch.insert(
            _db.courseSpecificationsTable,
            CourseSpecificationsTableCompanion(
              id: Value(spec.id),
              creditUnit: Value(spec.creditUnit),
              units: Value(spec.units),
              courseStatus: Value(spec.courseStatus),
              courseStatusId: Value(spec.courseStatusId),
              semesterAccronym: Value(spec.semesterAccronym),
              semesterTitle: Value(spec.semesterTitle),
              status: Value(spec.status),
              passMarkRequired: Value(spec.passMarkRequired),
              isSiwes: Value(spec.isSiwes),
              hasPreRequisites: Value(spec.hasPreRequisites),
              isUsedBeyondQualifierLevel:
                  Value(spec.isUsedBeyondQualifierLevel),
              isUsedInResultComputation: Value(spec.isUsedInResultComputation),
              sessionId: Value(spec.session.id),
              courseId: Value(spec.course.id),
            ),
            mode: InsertMode.insertOrReplace,
          );
        }
      });

      logger.i('Successfully saved ${specs.length} course specifications');
    } catch (e) {
      logger.e('Error saving course specifications: $e');
      rethrow;
    }
  }

  Future<List<CourseSpecification>> getCourseSpecifications(
      int? sessionId) async {
    try {
      final query = _db.select(_db.courseSpecificationsTable)
        ..where((tbl) {
          if (sessionId != null) {
            return tbl.sessionId.equals(sessionId);
          }
          return const Constant(true);
        });

      final specRows = await query.get();
      if (specRows.isEmpty) {
        logger.i('No course specifications found in cache');
        return [];
      }

      // Fetch related courses
      final courseIds = specRows.map((e) => e.courseId).toSet();
      final courseQuery = _db.select(_db.coursesTable)
        ..where((tbl) => tbl.id.isIn(courseIds));
      final courseRows = await courseQuery.get();

      // Create a map of course ID to Course object
      final courses = {
        for (final c in courseRows)
          c.id: Course(
            id: c.id,
            courseCode: c.courseCode,
            courseTitle: c.courseTitle,
            creditUnit: c.creditUnit,
            courseSynopsis: c.courseSynopsis,
            type: c.type,
            host: null, // This would need to be populated if needed
            department: null, // This would need to be populated if needed
            passMark: c.passMark,
            lecturers: (c.lecturers ?? '')
                .split(',')
                .where((e) => e.isNotEmpty)
                .toList(),
          )
      };

      // For now, we'll return empty sessions since they might not be needed
      // You would need to fetch sessions if they're required
      final emptySession = Session(
        id: 0,
        sessionName: 'Unknown',
        semesters: [],
      );

      final specifications = specRows
          .map(
            (row) => CourseSpecification(
              id: row.id,
              creditUnit: row.creditUnit,
              units: row.units,
              courseStatus: row.courseStatus,
              courseStatusId: row.courseStatusId,
              semesterAccronym: row.semesterAccronym,
              semesterTitle: row.semesterTitle,
              status: row.status,
              passMarkRequired: row.passMarkRequired,
              isSiwes: row.isSiwes,
              hasPreRequisites: row.hasPreRequisites,
              isUsedBeyondQualifierLevel: row.isUsedBeyondQualifierLevel,
              isUsedInResultComputation: row.isUsedInResultComputation,
              session: emptySession, // Use empty session for now
              course: courses[row.courseId] ??
                  Course(
                    id: row.courseId,
                    courseCode: 'Unknown',
                    courseTitle: 'Unknown',
                    creditUnit: 0,
                    courseSynopsis: '',
                    type: '',
                    host: null,
                    department: null,
                    passMark: 0,
                    lecturers: [],
                  ),
            ),
          )
          .toList();

      logger.i('Retrieved ${specifications.length} course specifications');
      return specifications;
    } catch (e) {
      logger.e('Error retrieving course specifications: $e');
      rethrow;
    }
  }
}
