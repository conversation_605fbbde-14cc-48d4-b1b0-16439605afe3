import 'package:drift/drift.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:tertiary_mobile/core/utils/local_database.dart';
import 'package:tertiary_mobile/core/utils/logger.dart';
import '../models/credit_load_limit.dart';

part 'local_credit_load_limits_source.g.dart';

@riverpod
LocalCreditLoadLimitsSource localCreditLoadLimitsSource(Ref ref) {
  final db = ref.read(localDatabaseProvider);
  return LocalCreditLoadLimitsSource(db);
}

class LocalCreditLoadLimitsSource {
  final LocalDatabase _db;
  LocalCreditLoadLimitsSource(this._db);

  /// Save credit load limits to local database
  Future<void> saveCreditLoadLimits(List<CreditLoadLimit> limits) async {
    try {
      await _db.transaction(() async {
        // Clear existing data
        await _db.delete(_db.creditLoadLimitsTable).go();

        // Insert new data
        for (final limit in limits) {
          await _db.into(_db.creditLoadLimitsTable).insert(
                CreditLoadLimitsTableCompanion.insert(
                  id: Value(limit.id),
                  maxCreditUnit: limit.maxCreditUnit,
                  maxExtraCreditUnit: limit.maxExtraCreditUnit,
                  minCreditUnit: limit.minCreditUnit,
                  programmeId: limit.programmeId,
                  studentAcademicLevelId: limit.studentAcademicLevelId,
                  semesterSettingId: Value(limit.semesterSettingId),
                ),
              );
        }
      });

      logger.i('Saved ${limits.length} credit load limits to local database');
    } catch (e) {
      logger.e('Error saving credit load limits: $e');
      rethrow;
    }
  }

  /// Get credit load limits from local database
  Future<List<CreditLoadLimit>> getCreditLoadLimits() async {
    try {
      final results = await _db.select(_db.creditLoadLimitsTable).get();

      return results
          .map((row) => CreditLoadLimit(
                id: row.id,
                maxCreditUnit: row.maxCreditUnit,
                maxExtraCreditUnit: row.maxExtraCreditUnit,
                minCreditUnit: row.minCreditUnit,
                programmeId: row.programmeId,
                studentAcademicLevelId: row.studentAcademicLevelId,
                semesterSettingId: row.semesterSettingId,
                semesterSetting:
                    null, // Will be populated by repository if needed
              ))
          .toList();
    } catch (e) {
      logger.e('Error getting credit load limits: $e');
      return [];
    }
  }

  /// Get credit load limits for a specific programme and academic level
  Future<List<CreditLoadLimit>> getCreditLoadLimitsByProgrammeAndLevel(
    int programmeId,
    int studentAcademicLevelId,
  ) async {
    try {
      final results = await (_db.select(_db.creditLoadLimitsTable)
            ..where((tbl) =>
                tbl.programmeId.equals(programmeId) &
                tbl.studentAcademicLevelId.equals(studentAcademicLevelId)))
          .get();

      return results
          .map((row) => CreditLoadLimit(
                id: row.id,
                maxCreditUnit: row.maxCreditUnit,
                maxExtraCreditUnit: row.maxExtraCreditUnit,
                minCreditUnit: row.minCreditUnit,
                programmeId: row.programmeId,
                studentAcademicLevelId: row.studentAcademicLevelId,
                semesterSettingId: row.semesterSettingId,
                semesterSetting: null,
              ))
          .toList();
    } catch (e) {
      logger.e('Error getting credit load limits by programme and level: $e');
      return [];
    }
  }

  /// Clear all credit load limits
  Future<void> clearCreditLoadLimits() async {
    try {
      await _db.delete(_db.creditLoadLimitsTable).go();
      logger.i('Cleared all credit load limits from local database');
    } catch (e) {
      logger.e('Error clearing credit load limits: $e');
      rethrow;
    }
  }
}
