import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:tertiary_mobile/core/constants/endpoints.dart';
import 'package:tertiary_mobile/core/network/providers/api_client.dart';
import 'package:tertiary_mobile/core/utils/logger.dart';
import 'package:tertiary_mobile/features/courses/data/models/course_registration_model.dart';
import 'package:tertiary_mobile/features/courses/data/models/course_specification_response.dart';

part 'remote_courses_source.g.dart';

@riverpod
class RemoteCoursesSource extends _$RemoteCoursesSource {
  @override
  RemoteCoursesSource build() => this;

  Future<List<CourseRegistration>> fetchCourseRegistrations(
      int? sessionId) async {
    try {
      final apiService = ref.read(apiClientProvider);
      final response = await apiService.get(ApiEndpoints.mycourses,
          queryParameters: {"session_id": sessionId});

      final data = response.data;
      if (data is! List) return [];
      if (data.isEmpty) return [];

      final List<CourseRegistration> registrations = [];

      for (final sessionData in data) {
        if (sessionData is! Map) continue;

        final session = sessionData['session'];
        if (session is! Map) continue;

        final courseRegs = session['course_registrations'];
        if (courseRegs is! List) continue;

        for (final reg in courseRegs) {
          if (reg is! Map) continue;
          try {
            registrations
                .add(CourseRegistration.fromJson(reg as Map<String, dynamic>));
          } catch (e) {
            logger.e('Failed to parse registration: $e');
            logger.e('Problematic registration data: $reg');
          }
        }
      }

      return registrations;
    } catch (e, st) {
      logger.e('Error fetching course registrations: $e\n$st');
      rethrow;
    }
  }

  Future<CourseSpecificationResponse> fetchCourseSpecifications(
      int? sessionId) async {
    try {
      final apiService = ref.read(apiClientProvider);
      final response = await apiService.get(ApiEndpoints.courseSpecifications,
          queryParameters: {"session_id": sessionId, "status": 1});

      final data = response.data;
      if (data is! List || data.isEmpty) {
        return const CourseSpecificationResponse(
          sessionId: 0,
          sessionName: '',
          courseSpecifications: [],
          creditLoadLimits: [],
        );
      }

      final sessionData = data.first as Map<String, dynamic>;
      return CourseSpecificationResponse.fromJson(sessionData);
    } catch (e, st) {
      logger.e('Error fetching course specifications: $e\n$st');
      rethrow;
    }
  }

  Future<void> submitCourseRegistrations(
      List<Map<String, dynamic>> registrations) async {
    try {
      logger.d('Submitting course registrations: $registrations');
      final apiService = ref.read(apiClientProvider);
      await apiService.post(
        ApiEndpoints.courseRegistration,
        data: {'courseReg': registrations},
      );
    } catch (e, st) {
      logger.e('Error submitting course registrations: $e\n$st');
      rethrow;
    }
  }
}
