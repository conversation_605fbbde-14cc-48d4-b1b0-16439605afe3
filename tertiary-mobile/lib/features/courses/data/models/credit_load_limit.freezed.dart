// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'credit_load_limit.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

CreditLoadLimit _$CreditLoadLimitFromJson(Map<String, dynamic> json) {
  return _CreditLoadLimit.fromJson(json);
}

/// @nodoc
mixin _$CreditLoadLimit {
  int get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'max_credit_unit')
  int get maxCreditUnit => throw _privateConstructorUsedError;
  @JsonKey(name: 'max_extra_credit_unit')
  int get maxExtraCreditUnit => throw _privateConstructorUsedError;
  @JsonKey(name: 'min_credit_unit')
  int get minCreditUnit => throw _privateConstructorUsedError;
  @JsonKey(name: 'programme_id')
  int get programmeId => throw _privateConstructorUsedError;
  @JsonKey(name: 'student_academic_level_id')
  int get studentAcademicLevelId => throw _privateConstructorUsedError;
  @JsonKey(name: 'semester_setting_id')
  int? get semesterSettingId => throw _privateConstructorUsedError;
  @JsonKey(name: 'semester_setting')
  Semester? get semesterSetting => throw _privateConstructorUsedError;

  /// Serializes this CreditLoadLimit to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CreditLoadLimit
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CreditLoadLimitCopyWith<CreditLoadLimit> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CreditLoadLimitCopyWith<$Res> {
  factory $CreditLoadLimitCopyWith(
          CreditLoadLimit value, $Res Function(CreditLoadLimit) then) =
      _$CreditLoadLimitCopyWithImpl<$Res, CreditLoadLimit>;
  @useResult
  $Res call(
      {int id,
      @JsonKey(name: 'max_credit_unit') int maxCreditUnit,
      @JsonKey(name: 'max_extra_credit_unit') int maxExtraCreditUnit,
      @JsonKey(name: 'min_credit_unit') int minCreditUnit,
      @JsonKey(name: 'programme_id') int programmeId,
      @JsonKey(name: 'student_academic_level_id') int studentAcademicLevelId,
      @JsonKey(name: 'semester_setting_id') int? semesterSettingId,
      @JsonKey(name: 'semester_setting') Semester? semesterSetting});

  $SemesterCopyWith<$Res>? get semesterSetting;
}

/// @nodoc
class _$CreditLoadLimitCopyWithImpl<$Res, $Val extends CreditLoadLimit>
    implements $CreditLoadLimitCopyWith<$Res> {
  _$CreditLoadLimitCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CreditLoadLimit
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? maxCreditUnit = null,
    Object? maxExtraCreditUnit = null,
    Object? minCreditUnit = null,
    Object? programmeId = null,
    Object? studentAcademicLevelId = null,
    Object? semesterSettingId = freezed,
    Object? semesterSetting = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      maxCreditUnit: null == maxCreditUnit
          ? _value.maxCreditUnit
          : maxCreditUnit // ignore: cast_nullable_to_non_nullable
              as int,
      maxExtraCreditUnit: null == maxExtraCreditUnit
          ? _value.maxExtraCreditUnit
          : maxExtraCreditUnit // ignore: cast_nullable_to_non_nullable
              as int,
      minCreditUnit: null == minCreditUnit
          ? _value.minCreditUnit
          : minCreditUnit // ignore: cast_nullable_to_non_nullable
              as int,
      programmeId: null == programmeId
          ? _value.programmeId
          : programmeId // ignore: cast_nullable_to_non_nullable
              as int,
      studentAcademicLevelId: null == studentAcademicLevelId
          ? _value.studentAcademicLevelId
          : studentAcademicLevelId // ignore: cast_nullable_to_non_nullable
              as int,
      semesterSettingId: freezed == semesterSettingId
          ? _value.semesterSettingId
          : semesterSettingId // ignore: cast_nullable_to_non_nullable
              as int?,
      semesterSetting: freezed == semesterSetting
          ? _value.semesterSetting
          : semesterSetting // ignore: cast_nullable_to_non_nullable
              as Semester?,
    ) as $Val);
  }

  /// Create a copy of CreditLoadLimit
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SemesterCopyWith<$Res>? get semesterSetting {
    if (_value.semesterSetting == null) {
      return null;
    }

    return $SemesterCopyWith<$Res>(_value.semesterSetting!, (value) {
      return _then(_value.copyWith(semesterSetting: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$CreditLoadLimitImplCopyWith<$Res>
    implements $CreditLoadLimitCopyWith<$Res> {
  factory _$$CreditLoadLimitImplCopyWith(_$CreditLoadLimitImpl value,
          $Res Function(_$CreditLoadLimitImpl) then) =
      __$$CreditLoadLimitImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int id,
      @JsonKey(name: 'max_credit_unit') int maxCreditUnit,
      @JsonKey(name: 'max_extra_credit_unit') int maxExtraCreditUnit,
      @JsonKey(name: 'min_credit_unit') int minCreditUnit,
      @JsonKey(name: 'programme_id') int programmeId,
      @JsonKey(name: 'student_academic_level_id') int studentAcademicLevelId,
      @JsonKey(name: 'semester_setting_id') int? semesterSettingId,
      @JsonKey(name: 'semester_setting') Semester? semesterSetting});

  @override
  $SemesterCopyWith<$Res>? get semesterSetting;
}

/// @nodoc
class __$$CreditLoadLimitImplCopyWithImpl<$Res>
    extends _$CreditLoadLimitCopyWithImpl<$Res, _$CreditLoadLimitImpl>
    implements _$$CreditLoadLimitImplCopyWith<$Res> {
  __$$CreditLoadLimitImplCopyWithImpl(
      _$CreditLoadLimitImpl _value, $Res Function(_$CreditLoadLimitImpl) _then)
      : super(_value, _then);

  /// Create a copy of CreditLoadLimit
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? maxCreditUnit = null,
    Object? maxExtraCreditUnit = null,
    Object? minCreditUnit = null,
    Object? programmeId = null,
    Object? studentAcademicLevelId = null,
    Object? semesterSettingId = freezed,
    Object? semesterSetting = freezed,
  }) {
    return _then(_$CreditLoadLimitImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      maxCreditUnit: null == maxCreditUnit
          ? _value.maxCreditUnit
          : maxCreditUnit // ignore: cast_nullable_to_non_nullable
              as int,
      maxExtraCreditUnit: null == maxExtraCreditUnit
          ? _value.maxExtraCreditUnit
          : maxExtraCreditUnit // ignore: cast_nullable_to_non_nullable
              as int,
      minCreditUnit: null == minCreditUnit
          ? _value.minCreditUnit
          : minCreditUnit // ignore: cast_nullable_to_non_nullable
              as int,
      programmeId: null == programmeId
          ? _value.programmeId
          : programmeId // ignore: cast_nullable_to_non_nullable
              as int,
      studentAcademicLevelId: null == studentAcademicLevelId
          ? _value.studentAcademicLevelId
          : studentAcademicLevelId // ignore: cast_nullable_to_non_nullable
              as int,
      semesterSettingId: freezed == semesterSettingId
          ? _value.semesterSettingId
          : semesterSettingId // ignore: cast_nullable_to_non_nullable
              as int?,
      semesterSetting: freezed == semesterSetting
          ? _value.semesterSetting
          : semesterSetting // ignore: cast_nullable_to_non_nullable
              as Semester?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CreditLoadLimitImpl implements _CreditLoadLimit {
  const _$CreditLoadLimitImpl(
      {required this.id,
      @JsonKey(name: 'max_credit_unit') required this.maxCreditUnit,
      @JsonKey(name: 'max_extra_credit_unit') required this.maxExtraCreditUnit,
      @JsonKey(name: 'min_credit_unit') required this.minCreditUnit,
      @JsonKey(name: 'programme_id') required this.programmeId,
      @JsonKey(name: 'student_academic_level_id')
      required this.studentAcademicLevelId,
      @JsonKey(name: 'semester_setting_id') this.semesterSettingId,
      @JsonKey(name: 'semester_setting') this.semesterSetting});

  factory _$CreditLoadLimitImpl.fromJson(Map<String, dynamic> json) =>
      _$$CreditLoadLimitImplFromJson(json);

  @override
  final int id;
  @override
  @JsonKey(name: 'max_credit_unit')
  final int maxCreditUnit;
  @override
  @JsonKey(name: 'max_extra_credit_unit')
  final int maxExtraCreditUnit;
  @override
  @JsonKey(name: 'min_credit_unit')
  final int minCreditUnit;
  @override
  @JsonKey(name: 'programme_id')
  final int programmeId;
  @override
  @JsonKey(name: 'student_academic_level_id')
  final int studentAcademicLevelId;
  @override
  @JsonKey(name: 'semester_setting_id')
  final int? semesterSettingId;
  @override
  @JsonKey(name: 'semester_setting')
  final Semester? semesterSetting;

  @override
  String toString() {
    return 'CreditLoadLimit(id: $id, maxCreditUnit: $maxCreditUnit, maxExtraCreditUnit: $maxExtraCreditUnit, minCreditUnit: $minCreditUnit, programmeId: $programmeId, studentAcademicLevelId: $studentAcademicLevelId, semesterSettingId: $semesterSettingId, semesterSetting: $semesterSetting)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreditLoadLimitImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.maxCreditUnit, maxCreditUnit) ||
                other.maxCreditUnit == maxCreditUnit) &&
            (identical(other.maxExtraCreditUnit, maxExtraCreditUnit) ||
                other.maxExtraCreditUnit == maxExtraCreditUnit) &&
            (identical(other.minCreditUnit, minCreditUnit) ||
                other.minCreditUnit == minCreditUnit) &&
            (identical(other.programmeId, programmeId) ||
                other.programmeId == programmeId) &&
            (identical(other.studentAcademicLevelId, studentAcademicLevelId) ||
                other.studentAcademicLevelId == studentAcademicLevelId) &&
            (identical(other.semesterSettingId, semesterSettingId) ||
                other.semesterSettingId == semesterSettingId) &&
            (identical(other.semesterSetting, semesterSetting) ||
                other.semesterSetting == semesterSetting));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      maxCreditUnit,
      maxExtraCreditUnit,
      minCreditUnit,
      programmeId,
      studentAcademicLevelId,
      semesterSettingId,
      semesterSetting);

  /// Create a copy of CreditLoadLimit
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CreditLoadLimitImplCopyWith<_$CreditLoadLimitImpl> get copyWith =>
      __$$CreditLoadLimitImplCopyWithImpl<_$CreditLoadLimitImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CreditLoadLimitImplToJson(
      this,
    );
  }
}

abstract class _CreditLoadLimit implements CreditLoadLimit {
  const factory _CreditLoadLimit(
          {required final int id,
          @JsonKey(name: 'max_credit_unit') required final int maxCreditUnit,
          @JsonKey(name: 'max_extra_credit_unit')
          required final int maxExtraCreditUnit,
          @JsonKey(name: 'min_credit_unit') required final int minCreditUnit,
          @JsonKey(name: 'programme_id') required final int programmeId,
          @JsonKey(name: 'student_academic_level_id')
          required final int studentAcademicLevelId,
          @JsonKey(name: 'semester_setting_id') final int? semesterSettingId,
          @JsonKey(name: 'semester_setting') final Semester? semesterSetting}) =
      _$CreditLoadLimitImpl;

  factory _CreditLoadLimit.fromJson(Map<String, dynamic> json) =
      _$CreditLoadLimitImpl.fromJson;

  @override
  int get id;
  @override
  @JsonKey(name: 'max_credit_unit')
  int get maxCreditUnit;
  @override
  @JsonKey(name: 'max_extra_credit_unit')
  int get maxExtraCreditUnit;
  @override
  @JsonKey(name: 'min_credit_unit')
  int get minCreditUnit;
  @override
  @JsonKey(name: 'programme_id')
  int get programmeId;
  @override
  @JsonKey(name: 'student_academic_level_id')
  int get studentAcademicLevelId;
  @override
  @JsonKey(name: 'semester_setting_id')
  int? get semesterSettingId;
  @override
  @JsonKey(name: 'semester_setting')
  Semester? get semesterSetting;

  /// Create a copy of CreditLoadLimit
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CreditLoadLimitImplCopyWith<_$CreditLoadLimitImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
