// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'course_specification_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

CourseSpecificationResponse _$CourseSpecificationResponseFromJson(
    Map<String, dynamic> json) {
  return _CourseSpecificationResponse.fromJson(json);
}

/// @nodoc
mixin _$CourseSpecificationResponse {
  @JsonKey(name: 'session_id')
  int get sessionId => throw _privateConstructorUsedError;
  @JsonKey(name: 'session_name')
  String get sessionName => throw _privateConstructorUsedError;
  @JsonKey(name: 'course_specifications')
  List<CourseSpecification> get courseSpecifications =>
      throw _privateConstructorUsedError;
  @JsonKey(name: 'credit_load_limits')
  List<CreditLoadLimit> get creditLoadLimits =>
      throw _privateConstructorUsedError;

  /// Serializes this CourseSpecificationResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CourseSpecificationResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CourseSpecificationResponseCopyWith<CourseSpecificationResponse>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CourseSpecificationResponseCopyWith<$Res> {
  factory $CourseSpecificationResponseCopyWith(
          CourseSpecificationResponse value,
          $Res Function(CourseSpecificationResponse) then) =
      _$CourseSpecificationResponseCopyWithImpl<$Res,
          CourseSpecificationResponse>;
  @useResult
  $Res call(
      {@JsonKey(name: 'session_id') int sessionId,
      @JsonKey(name: 'session_name') String sessionName,
      @JsonKey(name: 'course_specifications')
      List<CourseSpecification> courseSpecifications,
      @JsonKey(name: 'credit_load_limits')
      List<CreditLoadLimit> creditLoadLimits});
}

/// @nodoc
class _$CourseSpecificationResponseCopyWithImpl<$Res,
        $Val extends CourseSpecificationResponse>
    implements $CourseSpecificationResponseCopyWith<$Res> {
  _$CourseSpecificationResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CourseSpecificationResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? sessionId = null,
    Object? sessionName = null,
    Object? courseSpecifications = null,
    Object? creditLoadLimits = null,
  }) {
    return _then(_value.copyWith(
      sessionId: null == sessionId
          ? _value.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as int,
      sessionName: null == sessionName
          ? _value.sessionName
          : sessionName // ignore: cast_nullable_to_non_nullable
              as String,
      courseSpecifications: null == courseSpecifications
          ? _value.courseSpecifications
          : courseSpecifications // ignore: cast_nullable_to_non_nullable
              as List<CourseSpecification>,
      creditLoadLimits: null == creditLoadLimits
          ? _value.creditLoadLimits
          : creditLoadLimits // ignore: cast_nullable_to_non_nullable
              as List<CreditLoadLimit>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CourseSpecificationResponseImplCopyWith<$Res>
    implements $CourseSpecificationResponseCopyWith<$Res> {
  factory _$$CourseSpecificationResponseImplCopyWith(
          _$CourseSpecificationResponseImpl value,
          $Res Function(_$CourseSpecificationResponseImpl) then) =
      __$$CourseSpecificationResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'session_id') int sessionId,
      @JsonKey(name: 'session_name') String sessionName,
      @JsonKey(name: 'course_specifications')
      List<CourseSpecification> courseSpecifications,
      @JsonKey(name: 'credit_load_limits')
      List<CreditLoadLimit> creditLoadLimits});
}

/// @nodoc
class __$$CourseSpecificationResponseImplCopyWithImpl<$Res>
    extends _$CourseSpecificationResponseCopyWithImpl<$Res,
        _$CourseSpecificationResponseImpl>
    implements _$$CourseSpecificationResponseImplCopyWith<$Res> {
  __$$CourseSpecificationResponseImplCopyWithImpl(
      _$CourseSpecificationResponseImpl _value,
      $Res Function(_$CourseSpecificationResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of CourseSpecificationResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? sessionId = null,
    Object? sessionName = null,
    Object? courseSpecifications = null,
    Object? creditLoadLimits = null,
  }) {
    return _then(_$CourseSpecificationResponseImpl(
      sessionId: null == sessionId
          ? _value.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as int,
      sessionName: null == sessionName
          ? _value.sessionName
          : sessionName // ignore: cast_nullable_to_non_nullable
              as String,
      courseSpecifications: null == courseSpecifications
          ? _value._courseSpecifications
          : courseSpecifications // ignore: cast_nullable_to_non_nullable
              as List<CourseSpecification>,
      creditLoadLimits: null == creditLoadLimits
          ? _value._creditLoadLimits
          : creditLoadLimits // ignore: cast_nullable_to_non_nullable
              as List<CreditLoadLimit>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CourseSpecificationResponseImpl
    implements _CourseSpecificationResponse {
  const _$CourseSpecificationResponseImpl(
      {@JsonKey(name: 'session_id') required this.sessionId,
      @JsonKey(name: 'session_name') required this.sessionName,
      @JsonKey(name: 'course_specifications')
      required final List<CourseSpecification> courseSpecifications,
      @JsonKey(name: 'credit_load_limits')
      final List<CreditLoadLimit> creditLoadLimits = const []})
      : _courseSpecifications = courseSpecifications,
        _creditLoadLimits = creditLoadLimits;

  factory _$CourseSpecificationResponseImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$CourseSpecificationResponseImplFromJson(json);

  @override
  @JsonKey(name: 'session_id')
  final int sessionId;
  @override
  @JsonKey(name: 'session_name')
  final String sessionName;
  final List<CourseSpecification> _courseSpecifications;
  @override
  @JsonKey(name: 'course_specifications')
  List<CourseSpecification> get courseSpecifications {
    if (_courseSpecifications is EqualUnmodifiableListView)
      return _courseSpecifications;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_courseSpecifications);
  }

  final List<CreditLoadLimit> _creditLoadLimits;
  @override
  @JsonKey(name: 'credit_load_limits')
  List<CreditLoadLimit> get creditLoadLimits {
    if (_creditLoadLimits is EqualUnmodifiableListView)
      return _creditLoadLimits;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_creditLoadLimits);
  }

  @override
  String toString() {
    return 'CourseSpecificationResponse(sessionId: $sessionId, sessionName: $sessionName, courseSpecifications: $courseSpecifications, creditLoadLimits: $creditLoadLimits)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CourseSpecificationResponseImpl &&
            (identical(other.sessionId, sessionId) ||
                other.sessionId == sessionId) &&
            (identical(other.sessionName, sessionName) ||
                other.sessionName == sessionName) &&
            const DeepCollectionEquality()
                .equals(other._courseSpecifications, _courseSpecifications) &&
            const DeepCollectionEquality()
                .equals(other._creditLoadLimits, _creditLoadLimits));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      sessionId,
      sessionName,
      const DeepCollectionEquality().hash(_courseSpecifications),
      const DeepCollectionEquality().hash(_creditLoadLimits));

  /// Create a copy of CourseSpecificationResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CourseSpecificationResponseImplCopyWith<_$CourseSpecificationResponseImpl>
      get copyWith => __$$CourseSpecificationResponseImplCopyWithImpl<
          _$CourseSpecificationResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CourseSpecificationResponseImplToJson(
      this,
    );
  }
}

abstract class _CourseSpecificationResponse
    implements CourseSpecificationResponse {
  const factory _CourseSpecificationResponse(
          {@JsonKey(name: 'session_id') required final int sessionId,
          @JsonKey(name: 'session_name') required final String sessionName,
          @JsonKey(name: 'course_specifications')
          required final List<CourseSpecification> courseSpecifications,
          @JsonKey(name: 'credit_load_limits')
          final List<CreditLoadLimit> creditLoadLimits}) =
      _$CourseSpecificationResponseImpl;

  factory _CourseSpecificationResponse.fromJson(Map<String, dynamic> json) =
      _$CourseSpecificationResponseImpl.fromJson;

  @override
  @JsonKey(name: 'session_id')
  int get sessionId;
  @override
  @JsonKey(name: 'session_name')
  String get sessionName;
  @override
  @JsonKey(name: 'course_specifications')
  List<CourseSpecification> get courseSpecifications;
  @override
  @JsonKey(name: 'credit_load_limits')
  List<CreditLoadLimit> get creditLoadLimits;

  /// Create a copy of CourseSpecificationResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CourseSpecificationResponseImplCopyWith<_$CourseSpecificationResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}
