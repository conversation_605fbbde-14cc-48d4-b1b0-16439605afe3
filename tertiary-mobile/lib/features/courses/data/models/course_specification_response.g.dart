// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'course_specification_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$CourseSpecificationResponseImpl _$$CourseSpecificationResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$CourseSpecificationResponseImpl(
      sessionId: (json['session_id'] as num).toInt(),
      sessionName: json['session_name'] as String,
      courseSpecifications: (json['course_specifications'] as List<dynamic>)
          .map((e) => CourseSpecification.fromJson(e as Map<String, dynamic>))
          .toList(),
      creditLoadLimits: (json['credit_load_limits'] as List<dynamic>?)
              ?.map((e) => CreditLoadLimit.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
    );

Map<String, dynamic> _$$CourseSpecificationResponseImplToJson(
        _$CourseSpecificationResponseImpl instance) =>
    <String, dynamic>{
      'session_id': instance.sessionId,
      'session_name': instance.sessionName,
      'course_specifications':
          instance.courseSpecifications.map((e) => e.toJson()).toList(),
      'credit_load_limits':
          instance.creditLoadLimits.map((e) => e.toJson()).toList(),
    };
