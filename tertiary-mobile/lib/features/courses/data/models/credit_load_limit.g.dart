// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'credit_load_limit.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$CreditLoadLimitImpl _$$CreditLoadLimitImplFromJson(
        Map<String, dynamic> json) =>
    _$CreditLoadLimitImpl(
      id: (json['id'] as num).toInt(),
      maxCreditUnit: (json['max_credit_unit'] as num).toInt(),
      maxExtraCreditUnit: (json['max_extra_credit_unit'] as num).toInt(),
      minCreditUnit: (json['min_credit_unit'] as num).toInt(),
      programmeId: (json['programme_id'] as num).toInt(),
      studentAcademicLevelId:
          (json['student_academic_level_id'] as num).toInt(),
      semesterSettingId: (json['semester_setting_id'] as num?)?.toInt(),
      semesterSetting: json['semester_setting'] == null
          ? null
          : Semester.fromJson(json['semester_setting'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$CreditLoadLimitImplToJson(
        _$CreditLoadLimitImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'max_credit_unit': instance.maxCreditUnit,
      'max_extra_credit_unit': instance.maxExtraCreditUnit,
      'min_credit_unit': instance.minCreditUnit,
      'programme_id': instance.programmeId,
      'student_academic_level_id': instance.studentAcademicLevelId,
      'semester_setting_id': instance.semesterSettingId,
      'semester_setting': instance.semesterSetting?.toJson(),
    };
