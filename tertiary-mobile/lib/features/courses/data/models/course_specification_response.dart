// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:tertiary_mobile/features/courses/data/models/course_specification_model.dart';
import 'package:tertiary_mobile/features/courses/data/models/credit_load_limit.dart';

part 'course_specification_response.freezed.dart';
part 'course_specification_response.g.dart';

@freezed
class CourseSpecificationResponse with _$CourseSpecificationResponse {
  const factory CourseSpecificationResponse({
    @Json<PERSON>ey(name: 'session_id') required int sessionId,
    @Json<PERSON>ey(name: 'session_name') required String sessionName,
    @JsonKey(name: 'course_specifications') required List<CourseSpecification> courseSpecifications,
    @JsonKey(name: 'credit_load_limits') @Default([]) List<CreditLoadLimit> creditLoadLimits,
  }) = _CourseSpecificationResponse;

  factory CourseSpecificationResponse.fromJson(Map<String, dynamic> json) =>
      _$CourseSpecificationResponseFromJson(json);
}
