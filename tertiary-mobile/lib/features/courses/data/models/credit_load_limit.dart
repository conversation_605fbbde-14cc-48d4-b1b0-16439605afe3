// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:tertiary_mobile/shared/data/models/session_semester_model.dart';

part 'credit_load_limit.freezed.dart';
part 'credit_load_limit.g.dart';

@freezed
class CreditLoadLimit with _$CreditLoadLimit {
  const factory CreditLoadLimit({
    required int id,
    @Json<PERSON>ey(name: 'max_credit_unit') required int maxCreditUnit,
    @<PERSON>son<PERSON>ey(name: 'max_extra_credit_unit') required int maxExtraCreditUnit,
    @<PERSON>sonKey(name: 'min_credit_unit') required int minCreditUnit,
    @Json<PERSON>ey(name: 'programme_id') required int programmeId,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'student_academic_level_id')
    required int studentAcademicLevelId,
    @<PERSON>son<PERSON>ey(name: 'semester_setting_id') int? semesterSettingId,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'semester_setting') Semester? semesterSetting,
  }) = _CreditLoadLimit;

  factory CreditLoadLimit.fromJson(Map<String, dynamic> json) =>
      _$CreditLoadLimitFromJson(json);
}
