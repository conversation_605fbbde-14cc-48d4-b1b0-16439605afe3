import 'package:drift/drift.dart';

class CreditLoadLimitsTable extends Table {
  IntColumn get id => integer()();
  IntColumn get maxCreditUnit => integer().named('max_credit_unit')();
  IntColumn get maxExtraCreditUnit => integer().named('max_extra_credit_unit')();
  IntColumn get minCreditUnit => integer().named('min_credit_unit')();
  IntColumn get programmeId => integer().named('programme_id')();
  IntColumn get studentAcademicLevelId => integer().named('student_academic_level_id')();
  IntColumn get semesterSettingId => integer().nullable().named('semester_setting_id')();

  @override
  Set<Column> get primaryKey => {id};
}
