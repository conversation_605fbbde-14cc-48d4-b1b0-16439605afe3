name: tertiary_mobile

description: "SchoolTry Tertiary Mobile App ."

publish_to: "none"

version: 1.0.0+1

environment:
  sdk: ">=3.0.0 <4.0.0"

dependencies:
  flutter:
    sdk: flutter

  cupertino_icons: ^1.0.8
  hooks_riverpod: ^2.6.1
  flutter_hooks: ^0.20.5
  riverpod_annotation: ^2.6.1
  go_router: ^14.6.3
  dio: ^5.7.0
  freezed: ^2.5.8
  json_serializable: ^6.9.3
  flutter_secure_storage: ^10.0.0-beta.3
  flutter_screenutil: ^5.9.3
  cached_network_image: ^3.4.1
  flutter_svg: ^2.0.17
  skeletonizer: ^1.4.3
  flutter_native_splash: ^2.4.4
  flutter_launcher_icons: ^0.14.3
  logger: ^2.5.0
  intl: ^0.20.1
  url_launcher: ^6.3.1
  package_info_plus: ^8.1.3
  device_info_plus: ^11.2.1
  connectivity_plus: ^6.1.2
  mocktail: ^1.0.4
  equatable: ^2.0.7
  shared_preferences: ^2.3.5
  path_provider: ^2.1.5
  flutter_dotenv: ^5.2.1
  flutter_flavor: ^3.1.4
  pretty_dio_logger: ^1.4.0
  fluttertoast: ^8.2.10
  internet_connection_checker: ^3.0.1
  sqflite: ^2.4.1
  freezed_annotation: ^2.4.4
  json_annotation: ^4.9.0
  google_fonts: ^6.2.1
  material_color_utilities: ^0.11.1
  launcher_name: ^1.0.2
  rename_app: ^1.6.2
  dev: ^1.0.0
  screenshot: ^3.0.0
  icons_launcher: ^3.0.0
  table_calendar: ^3.2.0
  barcode_widget: ^2.0.4
  drift: ^2.26.0
  sqlite3_flutter_libs: ^0.5.32
  path: ^1.9.0
  flutter_riverpod: ^2.6.1
  crypto: ^3.0.6
  drift_flutter: ^0.2.4

dev_dependencies:
  flutter_test:
    sdk: flutter
  integration_test:
    sdk: flutter
  flutter_driver:
    sdk: flutter
  flutter_lints: ^5.0.0
  riverpod_generator: ^2.6.4
  build_runner: ^2.4.14
  custom_lint: ^0.7.1
  riverpod_lint: ^2.6.4
  drift_dev: ^2.26.0

flutter:
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/branding/
    - assets/icons/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
