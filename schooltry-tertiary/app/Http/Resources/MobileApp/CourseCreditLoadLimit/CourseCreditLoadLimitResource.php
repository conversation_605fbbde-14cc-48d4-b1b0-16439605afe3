<?php

namespace App\Http\Resources\MobileApp\CourseCreditLoadLimit;

use Illuminate\Http\Resources\Json\JsonResource;

class CourseCreditLoadLimitResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'max_credit_unit' => $this->max_credit_unit,
            'max_extra_credit_unit' => $this->max_extra_credit_unit,
            'min_credit_unit' => $this->min_credit_unit,
            'programme_id' => $this->programme_id,
            'student_academic_level_id' => $this->student_academic_level_id,
            'semester_setting_id' => $this->semester_setting_id,
            'semester_setting' => $this->semesterSetting ? [
                'id' => $this->semesterSetting->id,
                'title' => $this->semesterSetting->title ?? 'N/A',
                'accronym' => $this->semesterSetting->accronym ?? 'N/A',
                'position' => $this->semesterSetting->position ?? 'N/A',
            ] : null,
        ];
    }
}
