<?php

namespace App\Http\Controllers\MobileApp\v1\Session;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\SessionsAndSemesters\SessionConfig;
use App\Models\SessionsAndSemesters\SemesterConfig;
use App\Models\Courses\CourseRegistration;
use App\Models\SessionsAndSemesters\Session;
use App\Http\Resources\SessionConfigResource;
use App\Models\SessionsAndSemesters\Semester;
use App\Http\Resources\MobileApp\Institution\SessionResourceCollection;
use App\Models\SessionsAndSemesters\SpecialSession;
use App\Models\Userables\Student;
use Illuminate\Support\Facades\Auth;


class SessionController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:sanctum')->except('getCurrentSession', 'index');
    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        // For mobile app, return student-specific sessions
        if (Auth::check() && Auth::user()->userable_type === 'App\Models\Userables\Student') {
            return $this->getStudentSessions();
        }

        // Fallback to generic sessions for non-student users
        $query_builder = Session::buildQueryFromRequestParams($request->filters ?? [], $request->search_string, $request->id, $request->id_name)
            ->when($request->include_semesters, function ($query) {
                $query->with('semesters');
            });
        $no_of_batches = ceil(($query_builder->count()) / 4000);
        $sessions = new SessionResourceCollection($query_builder->paginate($request->items_per_page));

        return response()->json([
            'sessions' => $sessions,
            'no_of_batches' => $no_of_batches,
            'pagination' => [
                'total'        => $sessions->total(),
                'per_page'     => $sessions->perPage(),
                'current_page' => $sessions->currentPage(),
                'last_page'    => $sessions->lastPage(),
                'from'         => $sessions->firstItem(),
                'to'           => $sessions->lastItem(),
            ]
        ], 200);
    }

    /**
     * Get sessions specific to the authenticated student
     * Returns sessions from admission year to current session
     */
    public function getStudentSessions()
    {
        try {
            $student = Student::with('admissionSession')->findOrFail(Auth::user()->userable_id);
            $sessions = $this->getSessionsForStudent($student);

            return response()->json($sessions, 200);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Unable to fetch student sessions',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get sessions for a specific student (from admission to current)
     * Same logic as web implementation
     */
    public function getSessionsForStudent($student)
    {
        $admission_session_id = $student->admission_session_id;
        $admission_session = Session::where('id', $admission_session_id)
            ->where('institute_id', $student->institute_id)
            ->first();

        $active_session_status = \config('enums.session_status')['Active'];
        $special_session = SpecialSession::with('session')->where([
            'programme_id' => $student->programme_id,
            'level_id' => $student->current_level_id,
            'status' => $active_session_status
        ])->first();

        $current_session = $special_session ? $special_session->session : Session::where('status', $active_session_status)
            ->where('institute_id', $student->institute_id)
            ->first();

        return Session::with('semesters.semester_setting', 'semesters.session')
            ->whereBetween('session_name', [$admission_session->session_name, $current_session->session_name])
            ->where('institute_id', $student->institute_id)
            ->where('status', '!=', config('enums.session_status')['Pending'])
            ->orderBy('session_name', 'asc')->get();
    }

    //----------------------------------------------------------------------------------------------------------
    public function getCurrentSession($institute_id = null)
    {
        $institute_id = $institute_id == 'null' ? null : $institute_id;
        $active_session_status = \config('enums.session_status')['Active'];

        return Session::with('semesters.semester_setting')
            ->where('status', $active_session_status)
            ->where('institute_id', $institute_id)
            ->first();
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
