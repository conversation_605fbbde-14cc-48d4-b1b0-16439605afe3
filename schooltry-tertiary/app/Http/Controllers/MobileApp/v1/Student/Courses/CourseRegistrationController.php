<?php

namespace App\Http\Controllers\MobileApp\v1\Student\Courses;

use App\Http\Controllers\Controller;
use App\Http\Resources\MobileApp\CourseSpecification\CourseSpecificationWithCourseResource;
use App\Http\Resources\MobileApp\CourseSpecification\CourseSpecificationWithCourseResourceCollection;
use App\Http\Resources\MobileApp\Institution\SessionResource;
use App\Http\Resources\MobileApp\Student\Course\CourseResource;
use App\Http\Resources\MobileApp\Student\Course\CourseResourceCollection;
use App\Http\Resources\MobileApp\Student\CourseRegistration\CourseRegistrationResourceCollection;
use App\Models\Courses\Course;
use App\Models\Courses\CourseRegistration;
use App\Models\Institutions\Culture;
use App\Models\Institutions\Institution;
use App\Models\SessionsAndSemesters\Session;
use App\Models\Userables\Student;
use App\Services\StudentCourseService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Nette\Utils\Json;

class CourseRegistrationController extends Controller
{
    public function index(Request $request): JsonResponse
    {
        $request->validate([
            'status' => 'sometimes|in:1,2,3',
            'semester_id' => 'sometimes|exists:semesters,id',
            'session_id' => 'sometimes|exists:sessions,id',
        ]);

        $student = auth()->user()->userable;

        $registrations = $student->courseRegistrations()->whereHas('session')
            ->when($request->semester_id, fn($query) => $query->where('semester_id', $request->semester_id))
            ->when($request->session_id, fn($query) => $query->where('session_id', $request->session_id))
            ->when($request->status, fn($query) => $query->where('approval_status', $request->status))
            ->orderBy('semester_id', 'asc')
            ->get();

        return response()->json(new CourseRegistrationResourceCollection($registrations));
    }

    public function store(Request $request)
    {

        $request->validate([
            'courseReg' => 'required|array',
            'courseReg.*.course_id' => 'required|exists:courses,id',
            'courseReg.*.session_id' => 'required|exists:sessions,id',
            'courseReg.*.semester_id' => 'required|exists:semesters,id',
        ]);
        $institution_class = get_class(new Institution());
        $student = auth()->user()->userable;
        $culture = Culture::where('cultureable_type', $institution_class)
            ->where('cultureable_id', auth()->user()->institution_id)
            ->first();

        $courseRegList = $request->input('courseReg');
        $numberOfCoursesAdded = 0;
        foreach ($courseRegList as $courseRegInput) {

            /********** Check to prevent duplicate registrations of a course in a session *****/
            if (CourseRegistration::where([
                'course_id'  => $courseRegInput['course_id'],
                'student_id' =>  $student->id,
                'session_id' =>  $courseRegInput['session_id'],
                'semester_id' =>  $courseRegInput['semester_id'],
            ])->exists()) {
                return response()->json([
                    'message' => 'Course has already been registered for this session/semester'
                ], 422);
            }

            $student = Student::where('id', $student->id)
                ->select('id', 'institution_id', 'institute_id')->first();

            $course = Course::find($courseRegInput['course_id']);
            if (!$course) {
                return response()->json(['message' => 'Course not found'], 404);
            }

            $reversed_status_keys = $this->reverseCourseStatusKeys(config('enums.course_status'));

            /********** create the requested course Registration if no duplicate exists*****/
            $newCourseRegistration = new CourseRegistration();
            $newCourseRegistration->student_id      = $student->id;
            $newCourseRegistration->session_id      = $courseRegInput['session_id'];
            $newCourseRegistration->semester_id     = $courseRegInput['semester_id'];
            $newCourseRegistration->credit_unit     = $course->credit_unit;
            $newCourseRegistration->course_id       = $courseRegInput['course_id'];
            $newCourseRegistration->course_status   = $course->status;
            $newCourseRegistration->approval_status = (bool) $culture->auto_approve_course_registration ? \config('enums.course_registration_approval_status')['Approved'] : \config('enums.course_registration_approval_status')['Pending'];
            $newCourseRegistration->institution_id  = $student->institution_id;
            $newCourseRegistration->institute_id    = $student->institute_id;
            $newCourseRegistration->save();

            $numberOfCoursesAdded++;
        }
        return response()->json(['message' => $numberOfCoursesAdded . ' new courses registered successfully'], 200);
    }

    function reverseCourseStatusKeys(array $courseStatusMap): array
    {
        $reversed = [];

        foreach ($courseStatusMap as $key => $value) {
            $reversed[$value] = $key;
        }

        return $reversed;
    }

    public function getCourseSpecification(Request $request)
    {
        $request->validate([
            'course_specification_type' => 'sometimes|in:required_courses,core_courses,elective_courses,elective_groups,required_core_elective_courses',
            'session_id' => 'sometimes|integer|exists:sessions,id'
        ]);
        $user = auth()->user();

        // Use the provided session_id if available, otherwise fall back to current academic session
        $sessionQuery = Session::where('institution_id', $user->institution_id)->with('semesters');

        if ($request->has('session_id')) {
            $sessionQuery->where('id', $request->session_id);
        } else {
            $sessionQuery->where('status', 2);
        }

        $current_academic_session_with_semesters = new SessionResource($sessionQuery->first());

        // 🔥 Resolve service and fetch course data
        $courseService = app(StudentCourseService::class);
        // $paginated_course_specifications = $courseService->getPaginatedCourseGroup(
        //     $user->userable,
        //     $current_academic_session_with_semesters->id,
        //     $current_academic_session_with_semesters->semesters->first()->id,
        //     $request->course_specification_type ?? 'required_courses'
        // );

        // $response_key = $request->course_specification_type ?? 'required_courses';
        $courseSpecifications = $courseService->getCourseSpecificationsBySessionAndSemester($user->userable, $current_academic_session_with_semesters->id, $current_academic_session_with_semesters->semesters->first()->id);

        // Fetch credit load limits for the student
        $creditLoadLimits = \App\Models\Courses\CourseCreditLoadLimit::with('semesterSetting')
            ->where('programme_id', $user->userable->programme_id)
            ->where('student_academic_level_id', $user->userable->current_level_id)
            ->get();

        // dd($courseSpecifications['required_courses']);

        $mergedCourses = collect([
            ...($courseSpecifications['required_courses'] ?? []),
            ...($courseSpecifications['core_courses'] ?? []),
            ...($courseSpecifications['elective_courses'] ?? []),
            ...($courseSpecifications['elective_groups'] ?? []),
            ...($courseSpecifications['required_core_elective_courses'] ?? []),
            ...($courseSpecifications['specialized_courses'] ?? []),
        ])->unique(function ($item) {
            return is_array($item) ? $item['id'] : $item->id;
        });

        $data = [
            'session_id' => $current_academic_session_with_semesters->id,
            'session_name' => $current_academic_session_with_semesters->session_name,
            'course_specifications' => CourseSpecificationWithCourseResource::collection($mergedCourses),
            'credit_load_limits' => \App\Http\Resources\MobileApp\CourseCreditLoadLimit\CourseCreditLoadLimitResource::collection($creditLoadLimits),
        ];
        // );
        return response()->json(
            [
                $data
                // 'course_specifications' => $courseSpecifications
                // 'course_specifications' => $courseSpecifications['required_courses']
                // 'course_specifications' => $paginated_course_specifications,
                // 'current_course_specifications' => new CourseSpecificationWithCourseResourceCollection(
                //     $courseSpecifications
                // ),
                // 'required_courses' => CourseSpecificationWithCourseResource::collection($courseSpecifications['required_courses']),
                // 'core_courses' => CourseSpecificationWithCourseResource::collection($courseSpecifications['core_courses']),
                // 'elective_courses' => CourseSpecificationWithCourseResource::collection($courseSpecifications['elective_courses']),
                // 'elective_groups' => CourseSpecificationWithCourseResource::collection($courseSpecifications['elective_groups']),
                // 'required_core_elective_courses' => CourseSpecificationWithCourseResource::collection($courseSpecifications['required_core_elective_courses']),
                // 'specialized_courses' => CourseSpecificationWithCourseResource::collection($courseSpecifications['specialized_courses']),
                // 'current_course_specifications' => new CourseSpecificationWithCourseResource(
                //     $courseSpecifications
                // ),
                // 'current_course_specifications' => $courseSpecifications
                // 'active_course_registration_for_current_session' => SessionResource::collection(
                //     Session::where('course_registration_status', 1)
                //         ->where('institute_id', $user->userable->institute_id)
                //         ->with('semesters.semester_setting')
                //         ->orderBy('session_name', 'desc')
                //         ->get()
                // ),
            ]
        );
    }

    public function getActiveCourseRegistration()
    {
        $user = auth()->user();
        $active_course_registration = SessionResource::collection(
            Session::where('course_registration_status', 1)
                ->where('institute_id', $user->userable->institute_id)
                ->with('semesters.semester_setting')
                ->orderBy('session_name', 'desc')
                ->get()
        );

        return response()->json(new CourseRegistrationResourceCollection($active_course_registration));
    }
}
